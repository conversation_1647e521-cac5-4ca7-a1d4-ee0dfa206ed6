// Path from root
// Using only for app config

const GOOGLE_CREDENTIALS = {
  DEVELOPMENT: {
    googleServicesJson: './configs/google-credentials/dev/google-services.json',
    googleServicesPlist: './configs/google-credentials/dev/GoogleService-Info.plist',
  },
  PRODUCTION: {
    googleServicesJson: './configs/google-credentials/prod/google-services.json',
    googleServicesPlist: './configs/google-credentials/prod/GoogleService-Info.plist',
  },
}

// Helper function to get the appropriate configuration based on environment
const getGoogleCredentials = () => {
  const environment = process.env.EXPO_PUBLIC_GOOGLE_CREDENTIALS_ENVIROMENT
  return environment === 'production'
    ? GOOGLE_CREDENTIALS.PRODUCTION
    : GOOGLE_CREDENTIALS.DEVELOPMENT
}

module.exports = { getGoogleCredentials }
