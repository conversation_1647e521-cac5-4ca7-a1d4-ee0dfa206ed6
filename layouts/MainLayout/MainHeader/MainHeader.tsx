import DefaultAvatarIcon from '@/assets/icons/default-avatar-icon.svg'
// import NotiBellIcon from '@/assets/icons/noti-bell-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { BLURHASH_CODE } from '@/constants/global.constant'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES } from '@/routes/appRoutes'
import { Media } from '@/types/media.type'
import { Link, LinkProps, useRouter } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'

export const MainHeader = () => {
  const { user, status } = useAuthentication()
  const router = useRouter()
  const avatarMedia = user?.avatar as Media

  const avatarURL = avatarMedia?.url || avatarMedia?.thumbnailURL || user?.oauthAvatar || ''

  const { t } = useTranslation()

  return (
    <View className=" border-b border-custom-neutral-100 bg-white  ">
      {/* This View */}
      <View className="flex-row items-center justify-between px-4 py-3">
        <Link
          className="flex-row items-center gap-x-2"
          href={
            user
              ? (APP_ROUTES.PROFILE.path as LinkProps['href'])
              : (APP_ROUTES.LOGIN.path as LinkProps['href'])
          }
          disabled={status === 'loading'}
          asChild
        >
          <TouchableOpacity>
            {user ? (
              <>
                {avatarURL ? (
                  <StyledExpoImage
                    source={{ uri: avatarURL }}
                    className="aspect-square h-10 w-10 rounded-full"
                    contentFit="cover"
                    transition={1000}
                    placeholder={BLURHASH_CODE}
                  />
                ) : (
                  <DefaultAvatarIcon width={36} height={36} />
                )}
                <Text size="body3" variant="primary">
                  {user?.name}
                </Text>
              </>
            ) : (
              <View className="flex flex-row items-center gap-x-2">
                <DefaultAvatarIcon width={36} height={36} />
                <Text variant="primary" size="link3">
                  {t('MES-06')}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        </Link>
        {/* <TouchableOpacity className="rounded-lg bg-neutral-50 p-2">
          <NotiBellIcon width={20} height={20} />
        </TouchableOpacity> */}
      </View>
    </View>
  )
}
