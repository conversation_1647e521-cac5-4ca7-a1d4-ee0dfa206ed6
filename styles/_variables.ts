// Layout and sizing variables converted from CSS
export const variables = {
  // Layout dimensions
  layout: {
    tabBarHeight: 65,
  },

  // Spacing
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },

  // Border radius
  borderRadius: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 24,
    round: 999,
  },

  // Font sizes
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 28,
  },

  // Font weights
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },

  // Line heights
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },

  // Shadow and elevation
  shadow: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 4,
      elevation: 4,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 8,
      elevation: 8,
    },
  },
} as const

// Type definitions for better TypeScript support
export type Variables = typeof variables
export type VariableCategory = keyof Variables
export type VariableKey<T extends VariableCategory> = keyof Variables[T]

// Utility functions for easier access
export const getVariable = <T extends VariableCategory>(category: T, key: VariableKey<T>) => {
  return variables[category][key]
}

// Specific utility functions for common use cases
export const getLayoutSize = (key: keyof typeof variables.layout): number => {
  return variables.layout[key]
}

export const getSpacing = (key: keyof typeof variables.spacing): number => {
  return variables.spacing[key]
}

export const getBorderRadius = (key: keyof typeof variables.borderRadius): number => {
  return variables.borderRadius[key]
}

export const getFontSize = (key: keyof typeof variables.fontSize): number => {
  return variables.fontSize[key]
}

export const getFontWeight = (key: keyof typeof variables.fontWeight): string => {
  return variables.fontWeight[key]
}

export const getLineHeight = (key: keyof typeof variables.lineHeight): number => {
  return variables.lineHeight[key]
}

export const getShadow = (key: keyof typeof variables.shadow) => {
  return variables.shadow[key]
}

// Export individual categories for convenience
export const { layout, spacing, borderRadius, fontSize, fontWeight, lineHeight, shadow } = variables

// Default export
export default variables
