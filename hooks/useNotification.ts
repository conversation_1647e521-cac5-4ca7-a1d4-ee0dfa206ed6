import { appNotificationService } from '@/services/app-notification/app-notification.service'
import { useNotificationStore } from '@/stores/NotificationStore/NotificationStore'
import { useCallback } from 'react'

export const useNotification = () => {
  const {
    expoPushToken,

    isRegistered,
    isLoading,
    error,
    registerForPushNotifications,
    setupNotificationListeners,
    cleanupNotificationListeners,
    reset,
  } = useNotificationStore()

  // Convenience methods for sending notifications
  const sendImmediateNotification = useCallback(async (title: string, body: string, data?: any) => {
    return appNotificationService.sendImmediateNotification(title, body, data)
  }, [])

  const sendDelayedNotification = useCallback(
    async (title: string, body: string, trigger: any, data?: any) => {
      return appNotificationService.sendDelayedNotification(title, body, trigger, data)
    },
    [],
  )

  const sendLocalNotification = useCallback(async (options: any) => {
    return appNotificationService.sendLocalNotification(options)
  }, [])

  const cancelAllNotifications = useCallback(async () => {
    return appNotificationService.cancelAllNotifications()
  }, [])

  const getScheduledNotifications = useCallback(async () => {
    return appNotificationService.getScheduledNotifications()
  }, [])

  return {
    // State
    expoPushToken,

    isRegistered,
    isLoading,
    error,

    // Actions
    registerForPushNotifications,
    setupNotificationListeners,
    cleanupNotificationListeners,
    reset,

    // Convenience methods
    sendImmediateNotification,
    sendDelayedNotification,
    sendLocalNotification,
    cancelAllNotifications,
    getScheduledNotifications,
  }
}
