import * as FileSystem from 'expo-file-system'
import * as MediaLibrary from 'expo-media-library'
import * as Sharing from 'expo-sharing'
import { useCallback, useState } from 'react'
import { Alert, Platform } from 'react-native'

interface SaveImageOptions {
  /**
   * Album name to save the image to
   * @default 'Hico'
   */
  albumName?: string
  /**
   * Whether to show success alerts when the image is saved
   * @default true
   */
  showSuccessAlert?: boolean
}

/**
 * Hook for saving images to gallery from base64 data
 */
export const useSaveImageToGallery = () => {
  const [isSaving, setIsSaving] = useState(false)

  /**
   * Sanitize filename to avoid special character issues
   */
  const sanitizeFilename = useCallback((name: string): string => {
    // Replace spaces and special characters with underscores
    return name.replace(/[^\w.-]/g, '_').replace(/_{2,}/g, '_') // Replace multiple underscores with a single one
  }, [])

  /**
   * Share a file using the device's share functionality
   */
  const shareFile = useCallback(async (fileUri: string, filename: string) => {
    try {
      // Double-check file exists and has content
      const fileInfo = await FileSystem.getInfoAsync(fileUri)

      if (!fileInfo.exists) {
        throw new Error('File does not exist')
      }

      if ((fileInfo as any).size === 0) {
        throw new Error('File is empty (0KB)')
      }

      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'image/jpeg',
          dialogTitle: `Save ${filename}`,
          UTI: 'public.image', // iOS UTI
        })
      } else {
        console.error('Sharing is not available on this device')
        Alert.alert('Error', 'Sharing is not available on this device')
      }
    } catch (error) {
      console.error('Sharing error:', error)
      Alert.alert(
        'Error',
        `Failed to share the image: ${error instanceof Error ? error.message : 'Unknown error'}`,
      )
    }
  }, [])

  /**
   * Save an image to the gallery from base64 data
   */
  const saveImage = useCallback(
    async ({
      image,
      filename,
      options,
      onSuccess,
      onError,
    }: {
      image: string
      filename: string
      options?: SaveImageOptions
      onSuccess?: () => void
      onError?: (error: string) => void
    }) => {
      const { albumName = 'Hico', showSuccessAlert = false } = options || {}

      // Validate image data
      if (!image) {
        console.error('Image data is missing')
        Alert.alert('Error', 'Image data is missing')
        onError?.('Image data is missing')
        return { success: false, error: 'Image data is missing' }
      }

      if (!filename) {
        console.error('Filename is missing')
        Alert.alert('Error', 'Filename is missing')
        onError?.('Filename is missing')
        return { success: false, error: 'Filename is missing' }
      }

      // Sanitize the filename to avoid special character issues
      const sanitizedFilename = sanitizeFilename(filename)

      // Check if the image data is valid base64
      if (image.length < 100) {
        console.error('Image data is too short, likely invalid')
        Alert.alert('Error', 'Invalid image data (too short)')
        onError?.('Invalid image data (too short)')
        return { success: false, error: 'Invalid image data (too short)' }
      }

      // Make sure the base64 string doesn't contain the data URI prefix
      if (image.startsWith('data:image/')) {
        const commaIndex = image.indexOf(',')
        if (commaIndex !== -1) {
          image = image.substring(commaIndex + 1)
        }
      }

      if (isSaving) {
        onError?.('Another save operation is in progress')
        return { success: false, error: 'Another save operation is in progress' } // Prevent multiple simultaneous saves
      }
      setIsSaving(true)

      try {
        // First save to cache directory (temporary)
        const tempFileUri = `${FileSystem.cacheDirectory}${sanitizedFilename}`

        try {
          // Try to write the file
          await FileSystem.writeAsStringAsync(tempFileUri, image, {
            encoding: FileSystem.EncodingType.Base64,
          })
        } catch (writeError) {
          console.error('Error writing image file:', writeError)
          throw new Error(
            `Failed to write image file: ${writeError instanceof Error ? writeError.message : 'Unknown error'}`,
          )
        }

        // Verify the file was written correctly
        const fileInfo = await FileSystem.getInfoAsync(tempFileUri)

        if (!fileInfo.exists || (fileInfo as any).size === 0) {
          // Try fallback method for iOS
          if (Platform.OS === 'ios') {
            // Create a temporary file with text content first
            const tmpPath = `${FileSystem.cacheDirectory}temp_${Date.now()}.txt`
            await FileSystem.writeAsStringAsync(tmpPath, 'test')

            // Then overwrite it with the image content
            await FileSystem.writeAsStringAsync(tempFileUri, image, {
              encoding: FileSystem.EncodingType.Base64,
            })

            // Check again
            const retryFileInfo = await FileSystem.getInfoAsync(tempFileUri)
            if (!retryFileInfo.exists || (retryFileInfo as any).size === 0) {
              throw new Error('Image file still empty after retry')
            }
          } else {
            throw new Error('Image file was not saved correctly or is empty')
          }
        }

        // Try to save to media library (gallery) first
        try {
          // Request permissions to access media library
          const { status } = await MediaLibrary.requestPermissionsAsync()

          if (status === 'granted') {
            // Save the image to the media library
            const asset = await MediaLibrary.createAssetAsync(tempFileUri)

            // Create an album if needed and add the asset to it
            const album = await MediaLibrary.getAlbumAsync(albumName)
            if (album === null) {
              await MediaLibrary.createAlbumAsync(albumName, asset, false)
            } else {
              await MediaLibrary.addAssetsToAlbumAsync([asset], album, false)
            }

            if (showSuccessAlert) {
              Alert.alert('Success', `Image "${filename}" has been saved to gallery`)
            }
            onSuccess?.()
            return { success: true, asset }
          }
          // If permission denied, fall back to the platform-specific methods
          console.error(
            'Media library permission denied, falling back to platform-specific methods',
          )
        } catch (mediaError) {
          console.error('Error saving to media library:', mediaError)
          // Continue with platform-specific methods if media library fails
        }

        // Fall back to platform-specific methods if media library fails
        if (Platform.OS === 'android') {
          // Android: Use Storage Access Framework
          try {
            const permissions =
              await FileSystem.StorageAccessFramework.requestDirectoryPermissionsAsync()

            if (permissions.granted) {
              const uri = await FileSystem.StorageAccessFramework.createFileAsync(
                permissions.directoryUri,
                sanitizedFilename,
                'image/jpeg',
              )

              // Read the temp file as base64 and write it to the final destination
              const fileContent = await FileSystem.readAsStringAsync(tempFileUri, {
                encoding: FileSystem.EncodingType.Base64,
              })

              await FileSystem.writeAsStringAsync(uri, fileContent, {
                encoding: FileSystem.EncodingType.Base64,
              })

              if (showSuccessAlert) {
                Alert.alert('Success', `Image "${filename}" has been saved successfully`)
              }
              onSuccess?.()
              return { success: true, uri }
            } else {
              // If directory permission was denied, use sharing as fallback
              await shareFile(tempFileUri, filename)
              onSuccess?.()
              return { success: true, shared: true }
            }
          } catch (error) {
            console.error('Android save error:', error)
            // Fallback to sharing if direct save fails
            await shareFile(tempFileUri, filename)
            onSuccess?.()
            return { success: true, shared: true }
          }
        } else {
          // iOS: Use sharing
          await shareFile(tempFileUri, filename)
          onSuccess?.()
          return { success: true, shared: true }
        }
      } catch (error) {
        console.error('Error saving image:', error)
        Alert.alert(
          'Error',
          `Failed to save the image: ${error instanceof Error ? error.message : 'Unknown error'}`,
        )
        onError?.(error instanceof Error ? error.message : 'Unknown error')
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        }
      } finally {
        setIsSaving(false)
      }
    },
    [isSaving, sanitizeFilename, shareFile],
  )

  return {
    saveImage,
    isSaving,
  }
}
