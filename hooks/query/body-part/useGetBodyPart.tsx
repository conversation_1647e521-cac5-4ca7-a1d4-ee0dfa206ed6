import { bodyPartService } from '@/services/handbook/body-part.service'
import { BodyPart } from '@/types/body-part.type'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { bodyPartQueryKeys } from './queryKeys'

export const useGetBodyParts = ({
  params = {},
  options = {},
  useQueryOptions,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<UseQueryOptions<PaginatedDocs<BodyPart> | null>, 'queryKey' | 'queryFn'>
} = {}) => {
  const {
    isError: isGetBodyPartError,
    isPending: isGetBodyPartLoading,
    data: bodyPart,
    ...rest
  } = useQuery({
    queryKey: [bodyPartQueryKeys['bodyParts'].base(), params],
    queryFn: async () =>
      bodyPartService.getBodyParts({
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetBodyPartError,
    isGetBodyPartLoading,
    bodyPart,
    ...rest,
  }
}
