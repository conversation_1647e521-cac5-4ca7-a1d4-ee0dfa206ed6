import { dietarySupplementService } from '@/services/product/dieatary-supplement.service'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { DietarySupplementCategory } from '@/types/product.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { dietarySupplementQueryKeys } from './queryKeys'

export const useGetDietarySupplementCategories = ({
  params = {},
  options = {},
  useQueryOptions,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<
    UseQueryOptions<PaginatedDocs<DietarySupplementCategory> | null>,
    'queryKey' | 'queryFn'
  >
} = {}) => {
  const {
    isError: isGetDietarySupplementCategoriesError,
    isPending: isGetDietarySupplementCategoriesLoading,
    data: dietarySupplementCategories,
    ...rest
  } = useQuery({
    queryKey: [dietarySupplementQueryKeys['dietarySupplementCategories'].base(), params],
    queryFn: async () =>
      dietarySupplementService.getDietarySupplementCategories({
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetDietarySupplementCategoriesError,
    isGetDietarySupplementCategoriesLoading,
    dietarySupplementCategories,
    ...rest,
  }
}
