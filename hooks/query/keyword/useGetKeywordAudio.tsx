import { useMutation } from '@tanstack/react-query'

import { keywordService } from '@/services/handbook/keyword.service'
import { GetKeywordAudioPayload } from '@/types/keyword.type'
import { useRef } from 'react'
import { keywordMutationKeys } from './queryKeys'

export const useGetKeywordAudio = () => {
  // Define an AbortController to cancel previous requests
  const abortControllerRef = useRef<AbortController | null>(null)

  const {
    isError: isGetKeywordAudioError,
    isPending: isGetKeywordAudioPending,
    mutate: getKeywordAudioMutation,
    ...rest
  } = useMutation({
    mutationKey: keywordMutationKeys['get-keyword-audio'].base(),
    mutationFn: (payload: GetKeywordAudioPayload) => {
      // Abort any ongoing request before initiating a new one
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create a new AbortController for the new request
      const abortController = new AbortController()
      abortControllerRef.current = abortController

      // Pass the signal from AbortController to the service method
      return keywordService.getKeywordAudio({
        payload,
        options: {
          signal: abortControllerRef.current.signal,
        },
      })
    },
  })

  return {
    isGetKeywordAudioError,
    isGetKeywordAudioPending,
    getKeywordAudioMutation,
    ...rest,
  }
}
