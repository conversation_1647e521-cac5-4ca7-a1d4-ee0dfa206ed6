import ArrowLeftIcon from '@/assets/profile/arrow-left.svg'
import CheckIcon from '@/assets/profile/check-icon.svg'
import { DatePickerField } from '@/components/ui/DatePickerField/DatePickerField'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/FormField/FormField'
import { CustomRadioIndicator, Radio, RadioGroup, RadioLabel } from '@/components/ui/Radio/Radio'
import { Text } from '@/components/ui/Text/Text'
import { TextInput } from '@/components/ui/TextInput/TextInput'
import { GenderEnum } from '@/enums/common.enum'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useLoadingScreen } from '@/hooks/common/useLoadingScreen'
import { userService } from '@/services/user/user.service'
import { Media } from '@/types/media.type'
import { zodResolver } from '@hookform/resolvers/zod'
import dayjs from 'dayjs'
import { useRouter } from 'expo-router'
import React, { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import { ScrollView } from 'react-native-gesture-handler'
import Toast from 'react-native-toast-message'
import { z } from 'zod'
import { EditProfileAvatar } from '../EditProfileAvatar/EditProfileAvatar'
export interface UserProfile {
  fullName: string
  gender: 'male' | 'female' | ''
  birthDate: string
  address: string
  avatar?: string
}

export const EditProfileForm = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const { user, fetchCurrentUser } = useAuthentication()

  const { primaryLanguage } = useAppLanguage()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { showLoading, hideLoading } = useLoadingScreen()
  const formSchema = z.object({
    fullName: z.string().min(1, { message: t('MES-523') }),
    gender: z.string().min(1, { message: t('MES-467') }),
    birthDate: z
      .string()
      .min(1, { message: t('MES-468') })
      .refine(
        (date) => {
          // Check if the date matches YYYY/MM/DD format
          const dateRegex = /^\d{4}\/\d{2}\/\d{2}$/
          return dateRegex.test(date)
        },
        { message: t('MES-666') },
      )
      .refine(
        (date) => {
          // Validate the date using dayjs with strict parsing
          const parsedDate = dayjs(date, 'YYYY/MM/DD', true)
          const isValid = parsedDate.isValid()

          return isValid
        },
        { message: t('MES-666') },
      )
      .refine(
        (date) => {
          const parsedDate = dayjs(date, 'YYYY/MM/DD', true)
          // Check if date is not in the future
          return parsedDate.isBefore(dayjs()) || parsedDate.isSame(dayjs(), 'day')
        },
        { message: t('MES-469') },
      ),
    address: z.string(),
    avatar: z.string().optional(),
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: user?.name || '',
      gender: (user?.gender as GenderEnum) || '',
      birthDate: '',
      address: '',
      avatar: '',
    },
    mode: 'onTouched',
  })

  const handleSave = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true)
    showLoading()
    const { fullName, gender, birthDate, address, avatar } = values
    try {
      await userService.updateUserProfile({
        name: fullName,
        gender: gender as GenderEnum,
        dob: birthDate,
        address,
        avatar: avatar ? { uri: avatar, name: 'avatar.jpg', type: 'image/jpeg' } : undefined,
      })
      Toast.show({
        type: 'success',
        text1: t('MES-236'),
      })
      await fetchCurrentUser({
        setStatusLoading: false,
      })
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: t('MES-237'),
      })
      console.error('Profile update error:', error)
    } finally {
      setIsSubmitting(false)
      hideLoading()
    }
  }

  const handleBack = () => {
    router.back()
  }

  useEffect(() => {
    // Get avatar URL from user object
    const avatarMedia = user?.avatar as Media
    const avatarUrl = avatarMedia?.url || avatarMedia?.thumbnailURL || user?.oauthAvatar || ''

    // Handle null/undefined dob properly
    let birthDate = ''
    if (user?.dob) {
      const dobDate = dayjs(user.dob)
      if (dobDate.isValid()) {
        birthDate = dobDate.format('YYYY/MM/DD')
      } else {
        birthDate = 'Invalid Date'
      }
    }

    form.reset({
      fullName: user?.name || '',
      gender: (user?.gender as GenderEnum) || '',
      birthDate,
      address: user?.address || '',
      avatar: avatarUrl,
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user])

  return (
    <ScrollView
      className="flex-1"
      showsVerticalScrollIndicator={false}
      automaticallyAdjustKeyboardInsets
      keyboardShouldPersistTaps="handled"
      contentInsetAdjustmentBehavior="automatic"
      scrollEventThrottle={16}
    >
      <EditProfileHeader onBack={handleBack} onSave={form.handleSubmit(handleSave)} />

      <Form {...form}>
        <View className="gap-4 p-4 pb-20">
          <FormField
            control={form.control}
            name="avatar"
            render={({ field }) => (
              <FormItem className="flex flex-col gap-1.5">
                <FormControl>
                  <EditProfileAvatar
                    avatar={field.value}
                    onAvatarChange={(uri) => field.onChange(uri)}
                    disabled={isSubmitting}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="fullName"
            render={({ field }) => (
              <FormItem className="flex flex-col gap-1.5">
                <FormLabel required>{t('MES-91')}</FormLabel>
                <FormControl>
                  <TextInput
                    placeholder={t('MES-648')}
                    onChangeText={field.onChange}
                    onBlur={field.onBlur}
                    value={field.value}
                    editable={!isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="gender"
            render={({ field }) => (
              <FormItem className="flex flex-col gap-1.5">
                <FormLabel required>{t('MES-95')}</FormLabel>
                <FormControl>
                  <RadioGroup
                    value={field.value}
                    onChange={field.onChange}
                    className="flex-row gap-4"
                    isDisabled={isSubmitting}
                  >
                    <Radio value={GenderEnum.MALE} isDisabled={isSubmitting}>
                      <CustomRadioIndicator isSelected={field.value === GenderEnum.MALE} />
                      <RadioLabel>{t('MES-92')}</RadioLabel>
                    </Radio>

                    <Radio value={GenderEnum.FEMALE} isDisabled={isSubmitting}>
                      <CustomRadioIndicator isSelected={field.value === GenderEnum.FEMALE} />
                      <RadioLabel>{t('MES-93')}</RadioLabel>
                    </Radio>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="birthDate"
            render={({ field }) => (
              <FormItem className="flex flex-col gap-1.5">
                <FormLabel required>{t('MES-96')}</FormLabel>
                <FormControl>
                  <DatePickerField
                    value={field.value}
                    onDateChange={(date) => {
                      field.onChange(date)
                      console.log('date', date)
                    }}
                    placeholder={'YYYY/MM/DD'}
                    locale={primaryLanguage}
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="address"
            render={({ field }) => (
              <FormItem className="flex flex-col gap-1.5">
                <FormLabel>{t('MES-97')}</FormLabel>
                <FormControl>
                  <TextInput
                    placeholder={t('MES-652')}
                    onChangeText={field.onChange}
                    onBlur={field.onBlur}
                    value={field.value}
                    multiline
                    editable={!isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </View>
      </Form>
    </ScrollView>
  )
}

// Header

interface EditProfileHeaderProps {
  onBack: () => void
  onSave: () => void
}

export const EditProfileHeader = ({ onBack, onSave }: EditProfileHeaderProps) => {
  const { t } = useTranslation()
  return (
    <View className="flex-row items-center justify-between bg-white px-4 py-3">
      <TouchableOpacity
        className="h-6 w-6 items-center justify-center"
        onPress={onBack}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <ArrowLeftIcon width={16} height={16} />
      </TouchableOpacity>

      <Text size="body3" variant="primary">
        {t('MES-59')}
      </Text>

      <TouchableOpacity
        className="h-6 w-6 items-center justify-center"
        onPress={onSave}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <CheckIcon width={16} height={16} />
      </TouchableOpacity>
    </View>
  )
}
