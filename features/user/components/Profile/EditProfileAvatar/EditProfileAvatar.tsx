import DefaultAvatarIcon from '@/assets/icons/default-avatar-icon.svg'
import EditIcon from '@/assets/profile/edit-icon.svg'
import { BLURHASH_CODE } from '@/constants/global.constant'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { StyledExpoImage } from '@/libs/styled'
import { Media } from '@/types/media.type'
import * as ImagePicker from 'expo-image-picker'
import React, { useState } from 'react'
import { TouchableOpacity, View } from 'react-native'
interface EditProfileAvatarProps {
  avatar?: string
  onAvatarChange: (uri: string) => void
  disabled?: boolean
}

export function EditProfileAvatar({ avatar, onAvatarChange, disabled }: EditProfileAvatarProps) {
  const [previewUri, setPreviewUri] = useState<string | undefined>(avatar)
  const { user } = useAuthentication()
  const avatarMedia = user?.avatar as Media
  const avatarURL = avatarMedia?.url || avatarMedia?.thumbnailURL || user?.oauthAvatar || ''
  const handleEditAvatar = async () => {
    try {
      // Request permission
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync()

      if (!permissionResult.granted) {
        alert('Permission to access gallery is required!')
        return
      }

      // Pick image
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.7,
      })

      if (!result.canceled) {
        const imageUri = result.assets[0].uri
        setPreviewUri(imageUri)
        onAvatarChange(imageUri)
      }
    } catch (error) {
      console.error('Error picking image:', error)
    }
  }

  return (
    <View className="relative flex items-center px-4 py-4">
      <TouchableOpacity
        className="relative rounded-full border border-custom-neutral-100 "
        onPress={handleEditAvatar}
        disabled={disabled}
      >
        {previewUri ? (
          <StyledExpoImage
            source={{ uri: previewUri }}
            className="aspect-square rounded-full"
            contentFit="cover"
            transition={400}
            placeholder={BLURHASH_CODE}
            style={{
              width: 120,
              height: 120,
              borderRadius: '100%',
            }}
            contentPosition="center"
          />
        ) : (
          <>
            {avatarURL ? (
              <StyledExpoImage
                source={{ uri: avatarURL }}
                style={{
                  width: 120,
                  height: 120,
                  borderRadius: '100%',
                }}
                contentFit="cover"
                className="aspect-square rounded-full"
                transition={400}
                contentPosition="center"
                placeholder={BLURHASH_CODE}
              />
            ) : (
              <DefaultAvatarIcon width={120} height={120} />
            )}
          </>
        )}

        <View
          style={{
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.25,
            shadowRadius: 3.84,
            position: 'absolute',
            bottom: 0,
            right: 0,
          }}
        >
          <EditIcon width={16} height={16} />
        </View>
      </TouchableOpacity>
    </View>
  )
}
