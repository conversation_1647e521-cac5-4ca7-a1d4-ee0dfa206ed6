import React from 'react'
import { ScrollView, View } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { CompanyInfo } from '../../components/Profile/CompanyInfo/CompanyInfo'
import { ProfileMenu } from '../../components/Profile/ProfileMenu/ProfileMenu'
import { SummaryInfo } from '../../components/Profile/SummaryInfo/SummaryInfo'

export const ProfileScreen = () => {
  return (
    <SafeAreaView className="flex-1 bg-white" edges={['bottom']}>
      <View className="flex flex-1 flex-col justify-between">
        <ScrollView className="flex-1">
          <View className="flex flex-col gap-y-3 p-3">
            <SummaryInfo />
            <ProfileMenu />
          </View>
        </ScrollView>

        <View className="px-3 pb-3">
          <CompanyInfo />
        </View>
      </View>
    </SafeAreaView>
  )
}
