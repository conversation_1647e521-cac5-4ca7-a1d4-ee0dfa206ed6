import GoogleIcon from '@/assets/auth/google-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { useLoadingScreen } from '@/hooks/common/useLoadingScreen'
import { APP_ROUTES } from '@/routes/appRoutes'
import { RelativePathString, useRouter } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import Toast from 'react-native-toast-message'

export const OAuthLogin = () => {
  const router = useRouter()
  const { t } = useTranslation()
  const { googleLogin } = useAuthentication()
  const { showLoading, hideLoading } = useLoadingScreen()
  const signIn = async () => {
    try {
      // Check if we're in production
      const isProd = process.env.EXPO_PUBLIC_APP_ENVIROMENT === 'production'
      if (!isProd) {
        Toast.show({
          type: 'error',
          text1: 'Google Sign-In is disabled in development',
        })
        return
      }

      // Get Google Sign-In components
      const {
        GoogleSignin,
        isSuccessResponse,
      } = require('@react-native-google-signin/google-signin')

      await GoogleSignin.hasPlayServices()
      await GoogleSignin.signOut()
      const response = await GoogleSignin.signIn()

      if (isSuccessResponse(response)) {
        const idToken = response?.data?.idToken

        if (idToken) {
          try {
            showLoading()

            await googleLogin(idToken)
            router.replace({
              pathname: APP_ROUTES.HOME.path as RelativePathString,
            })
          } catch (error) {
            console.log('signIn error', error)
          } finally {
            hideLoading()
          }
          return
        }
      }

      Toast.show({
        type: 'error',
        text1: t('MES-241'),
      })
    } catch (error) {
      console.log('signIn error', error)
      Toast.show({
        type: 'error',
        text1: t('MES-241'),
      })
    }
  }

  return (
    <View className="w-full items-center gap-2 py-3">
      <View className="w-[358px] flex-row items-center justify-center gap-2">
        <View className="h-px w-[70px] bg-custom-divider" />
        <Text size="body9" variant="subdued">
          {t('MES-664')}
        </Text>
        <View className="h-px w-[70px] bg-custom-divider" />
      </View>

      <TouchableOpacity
        className="flex-row gap-2 rounded-full border border-custom-neutral-80 bg-white p-3"
        onPress={() => {
          signIn()
        }}
      >
        <GoogleIcon width={24} height={24} />
      </TouchableOpacity>
    </View>
  )
}
