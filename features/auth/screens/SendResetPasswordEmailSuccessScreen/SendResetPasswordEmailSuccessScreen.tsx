import ArrowLeftIcon from '@/assets/auth/arrow-left-icon.svg'
import EmailNotiIcon from '@/assets/icons/email-noti-icon.svg'
import RedirectIcon from '@/assets/icons/redirect-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { APP_ROUTES } from '@/routes/appRoutes'
import { LinkProps, router } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Platform, SafeAreaView, TouchableOpacity, View } from 'react-native'

export const SendResetPasswordEmailSuccessScreen = () => {
  const { t } = useTranslation()

  const handleGoBack = () => {
    router.replace({
      pathname: APP_ROUTES.HOME.path,
    } as LinkProps['href'])
  }
  const handleGoToLogin = () => {
    router.replace({
      pathname: APP_ROUTES.LOGIN.path,
    } as LinkProps['href'])
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className=" h-[62px] flex-row items-center gap-3 p-4">
        <TouchableOpacity
          onPress={handleGoBack}
          className="h-6 w-6"
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <ArrowLeftIcon width={20} height={20} />
        </TouchableOpacity>
      </View>

      {/* Body */}
      <View className="flex-1 flex-col items-center gap-6 px-10 py-10">
        {/* Success Icon */}
        <View
          className="mb-10 flex items-center justify-center rounded-full bg-custom-warning-100 p-12"
          style={{
            backgroundColor: '#fffbeb',
            ...Platform.select({
              ios: {
                shadowColor: '#fffbeb',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.12,
                shadowRadius: 12,
              },
              android: {
                elevation: 12,
              },
            }),
          }}
        >
          <EmailNotiIcon />
        </View>

        {/* Title */}
        <Text variant="primary" size="heading7">
          {t('MES-657')}
        </Text>

        {/* Description */}
        <Text variant="subdued" size="body6" className="text-center">
          {t('MES-658')}
        </Text>

        <TouchableOpacity onPress={handleGoToLogin} className="mt-4 flex-row items-center gap-2">
          <Text variant="primary" size="body6" className="text-center">
            {t('MES-659')}
          </Text>
          <RedirectIcon width={16} height={16} />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  )
}
