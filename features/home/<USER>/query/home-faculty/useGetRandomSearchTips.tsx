import { homeFacultyService } from '@/features/home/<USER>/home-faculty.service'
import { Faculty } from '@/types/faculty.type'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { homeFacultyQueryKeys } from './queryKeys'

export const useGetHomeFaculties = ({
  params = {},
  options = {},
  useQueryOptions,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<UseQueryOptions<PaginatedDocs<Faculty> | null>, 'queryKey' | 'queryFn'>
} = {}) => {
  const {
    isError: isGetHomeFacultiesError,
    isPending: isGetHomeFacultiesLoading,
    data: homeFaculties,
    ...rest
  } = useQuery({
    queryKey: [homeFacultyQueryKeys['homeFaculties'].base(), params],
    queryFn: async () =>
      homeFacultyService.getHomeFaculties({
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetHomeFacultiesError,
    isGetHomeFacultiesLoading,
    homeFaculties,
    ...rest,
  }
}
