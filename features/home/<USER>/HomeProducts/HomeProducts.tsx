import DrugPillIcon from '@/assets/icons/drug-pill-icon.svg'
import HealthCareIcon from '@/assets/icons/health-care-icon.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/Tabs/Tabs'
import { Text } from '@/components/ui/Text/Text'
import { BLURHASH_CODE } from '@/constants/global.constant'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useGetBodyParts } from '@/hooks/query/body-part/useGetBodyPart'
import { useGetDietarySupplementCategories } from '@/hooks/query/dietary-supplement/useGetDietarySupplementCategories'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES } from '@/routes/appRoutes'
import { BodyPart } from '@/types/body-part.type'
import { Media } from '@/types/media.type'
import { DietarySupplementCategory } from '@/types/product.type'
import { cn } from '@/utils/cn'
import { Link, LinkProps } from 'expo-router'
import React, { useCallback, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Dimensions, FlatList, ScrollView, TouchableOpacity, View } from 'react-native'

const PANEL_ITEMS = {
  MEDICINE: {
    id: 'medicine',
    title: 'MES-557',
  },
  DIETARY_SUPPLEMENT: {
    id: 'Dietary Supplement',
    title: 'MES-471',
  },
}

const ITEMS_PER_PAGE = 9
const COLUMNS = 3
const { width: screenWidth } = Dimensions.get('window')

export const HomeProducts = () => {
  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()
  const [selectedCategory, setSelectedCategory] = useState(PANEL_ITEMS.MEDICINE.id)

  const { bodyPart, isGetBodyPartLoading } = useGetBodyParts({
    params: {
      locale: primaryLanguage,
      limit: 50,
      depth: 1,
      select: {
        name: true,
        id: true,
        heroImage: true,
      },
      where: {
        rootBodyPart: {
          equals: true,
        },
      },
    },
    useQueryOptions: {
      enabled: selectedCategory === PANEL_ITEMS.MEDICINE.id,
      staleTime: Infinity,
    },
  })

  const { dietarySupplementCategories, isGetDietarySupplementCategoriesLoading } =
    useGetDietarySupplementCategories({
      useQueryOptions: {
        enabled: selectedCategory === PANEL_ITEMS.DIETARY_SUPPLEMENT.id,
        staleTime: Infinity,
      },
      params: {
        limit: 25,
        depth: 1,
        draft: false,
        locale: primaryLanguage,
      },
    })

  const tabTriggerClass = useMemo(() => {
    return {
      default:
        'self-start min-w-[67px] border border-transparent rounded-[60px] overflow-hidden bg-custom-neutral-80 px-3 py-1 text-center',
      active: 'border border-primary bg-white text-primary',
      'text-default': 'text-custom-text-subdued text-center',
      'text-active': 'text-primary-500',
    }
  }, [])

  return (
    <View className="flex-1 px-4">
      <View className="mb-3 flex-row items-center gap-3">
        <Text size="heading8" variant="primary">
          {t('MES-558')}
        </Text>
        <DrugPillIcon className="h-6 w-6" />
      </View>

      <Tabs
        value={selectedCategory}
        onValueChange={(value) => {
          setSelectedCategory(value)
        }}
      >
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <TabsList className="flex-1 flex-row gap-x-2 self-start">
            {Object.values(PANEL_ITEMS).map((category) => (
              <TabsTrigger
                key={category.id}
                value={category.id}
                isActive={selectedCategory === category.id}
              >
                <View
                  className={cn(
                    tabTriggerClass['default'],
                    selectedCategory === category.id &&
                      'border !border-primary !bg-white text-primary',
                  )}
                >
                  <Text
                    size="body6"
                    className={cn(
                      tabTriggerClass['text-default'],
                      selectedCategory === category.id && tabTriggerClass['text-active'],
                    )}
                  >
                    {t(category.title)}
                  </Text>
                </View>
              </TabsTrigger>
            ))}
          </TabsList>
        </ScrollView>

        <TabsContent value={PANEL_ITEMS.MEDICINE.id} className="mt-4">
          {isGetBodyPartLoading ? (
            <ListLoading />
          ) : (
            <CardItem data={bodyPart?.docs || []} tab={PANEL_ITEMS.MEDICINE} />
          )}
        </TabsContent>

        <TabsContent value={PANEL_ITEMS.DIETARY_SUPPLEMENT.id} className="mt-4">
          {isGetDietarySupplementCategoriesLoading ? (
            <ListLoading />
          ) : (
            <CardItem
              data={dietarySupplementCategories?.docs || []}
              tab={PANEL_ITEMS.DIETARY_SUPPLEMENT}
            />
          )}
        </TabsContent>
      </Tabs>
    </View>
  )
}

const ListLoading = () => {
  const skeletonGrid = Array.from({ length: 3 }, () => new Array(3).fill(0))

  return (
    <View className="flex-1 flex-col gap-3 px-4">
      {skeletonGrid.map((row, rowIndex) => (
        <View key={rowIndex} className="flex-row justify-between gap-3">
          {row.map((_, colIndex) => (
            <View className="flex-1 flex-row items-center justify-center p-3" key={colIndex}>
              <Skeleton className="aspect-square h-[50px] rounded-lg bg-gray-200" />
            </View>
          ))}
        </View>
      ))}
    </View>
  )
}

interface CardItemProps {
  tab: (typeof PANEL_ITEMS)[keyof typeof PANEL_ITEMS]
  data: DietarySupplementCategory[] | BodyPart[]
}
const CardItem = ({ data, tab }: CardItemProps) => {
  const groupedData = useMemo(() => {
    const groups: (BodyPart[] | DietarySupplementCategory[])[] = []
    for (let i = 0; i < (data?.length || 0); i += ITEMS_PER_PAGE) {
      groups.push(data?.slice(i, i + ITEMS_PER_PAGE) || [])
    }
    return groups
  }, [data])

  const renderGroupedData = ({
    item: pageItems,
    tabId,
  }: {
    item: BodyPart[] | DietarySupplementCategory[]
    tabId: string
  }) => {
    if (tabId === PANEL_ITEMS.MEDICINE.id) {
      return (
        <View style={{ width: screenWidth - 32 }} className="mx-auto flex-1">
          <FlatList
            data={pageItems as BodyPart[]}
            renderItem={({ item }) => renderItem({ item, tabId })}
            numColumns={COLUMNS}
            columnWrapperStyle={{ gap: 12 }}
            contentContainerStyle={{
              paddingHorizontal: 8,
            }}
            scrollEnabled={false}
            keyExtractor={(item, index) => `${item.id}-${index}`}
          />
        </View>
      )
    }
    return (
      <View style={{ width: screenWidth - 32 }} className="mx-auto flex-1">
        <FlatList
          data={pageItems as DietarySupplementCategory[]}
          renderItem={({ item }) => renderItem({ item, tabId })}
          numColumns={COLUMNS}
          columnWrapperStyle={{ gap: 12 }}
          contentContainerStyle={{
            paddingHorizontal: 8,
          }}
          scrollEnabled={false}
          keyExtractor={(item, index) => `${item.id}-${index}`}
        />
      </View>
    )
  }

  const renderItem = useCallback(
    ({ item, tabId }: { item: BodyPart | DietarySupplementCategory; tabId: string }) => {
      const isMedicine = tabId === PANEL_ITEMS.MEDICINE.id
      const thumbnailMedia = !isMedicine
        ? ((item as DietarySupplementCategory).image as Media)
        : ((item as BodyPart).heroImage as Media)
      const thumbnailUrl = thumbnailMedia?.url || thumbnailMedia?.thumbnailURL || ''
      const title = !isMedicine
        ? (item as DietarySupplementCategory).title
        : (item as BodyPart).name

      const pathname = !isMedicine
        ? APP_ROUTES.PRODUCTS?.children?.PRODUCTS_LIST.path + '/[id]'
        : APP_ROUTES.PRODUCTS?.children?.MEDICINE_BODY_PARTS.path + '/[id]'

      const params = !isMedicine
        ? {
            id: item.id,
            s: 'true',
            type: 'dietary-supplement',
          }
        : {
            id: item.id,

            type: 'medicine',
          }

      return (
        <Link
          href={
            {
              pathname: pathname,
              params: params,
            } as LinkProps['href']
          }
          asChild
        >
          <TouchableOpacity
            className=" flex-col items-center justify-center "
            style={{
              width: (screenWidth - 32) / COLUMNS - 12,
            }}
          >
            <View className="mb-2 h-[50px] w-[50px] flex-row items-center justify-center rounded-[16px] bg-primary-50">
              {thumbnailUrl ? (
                <StyledExpoImage
                  source={thumbnailUrl}
                  contentFit="cover"
                  transition={300}
                  placeholder={BLURHASH_CODE}
                  className="h-8 w-8"
                />
              ) : (
                <HealthCareIcon className="h-8 w-8" />
              )}
            </View>
            <Text
              size="body8"
              className="line-clamp-2 min-h-[40px] px-1 text-center"
              numberOfLines={2}
            >
              {title}
            </Text>
          </TouchableOpacity>
        </Link>
      )
    },
    [],
  )

  if (tab.id === PANEL_ITEMS.MEDICINE.id) {
    return (
      <FlatList
        horizontal
        pagingEnabled
        data={groupedData as BodyPart[][]}
        renderItem={({ item }) => renderGroupedData({ item, tabId: tab.id })}
        showsHorizontalScrollIndicator={false}
        decelerationRate="fast"
        snapToInterval={screenWidth - 32}
        snapToAlignment="start"
        contentContainerStyle={{
          gap: 0,
        }}
        keyExtractor={(_, index) => `page-${index}-${tab.id}`}
      />
    )
  }
  return (
    <FlatList
      horizontal
      pagingEnabled
      data={groupedData as DietarySupplementCategory[][]}
      renderItem={({ item }) => renderGroupedData({ item, tabId: tab.id })}
      showsHorizontalScrollIndicator={false}
      decelerationRate="fast"
      snapToInterval={screenWidth - 32}
      snapToAlignment="start"
      contentContainerStyle={{
        gap: 0,
      }}
      keyExtractor={(_, index) => `page-${index}-${tab.id}`}
    />
  )
}
