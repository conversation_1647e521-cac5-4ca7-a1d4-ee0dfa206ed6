import React from 'react'
import { KeyboardAvoidingView, Platform, ScrollView, View } from 'react-native'
import { DailyFact } from '../../components/DailyFact/DailyFact'
import { DailyVocabulary } from '../../components/DailyVocabulary/DailyVocabulary'
import { FeatureList } from '../../components/FeatureList/FeatureList'
import { HomeFaculties } from '../../components/HomeFaculties/HomeFaculties'
import { HomePosts } from '../../components/HomePosts/HomePosts'
import { HomeProducts } from '../../components/HomeProducts/HomeProducts'
import { HomeSearch } from '../../components/HomeSearch/HomeSearch'

import { HomeEmailSubscription } from '../../components/HomeEmailSubscription/HomeEmailSubscription'
import { SearchMedIntro } from '../../components/SearchMedIntro/SearchMedIntro'

export default function HomeScreen() {
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1"
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        automaticallyAdjustKeyboardInsets
        keyboardShouldPersistTaps="handled"
        contentInsetAdjustmentBehavior="automatic"
        className="flex-1 bg-white"
      >
        <View className="flex-1 flex-col gap-4 pb-6">
          <HomeSearch />
          <SearchMedIntro />
          <FeatureList />
          <DailyVocabulary />
          <DailyFact />
          <HomeFaculties />
          <HomeProducts />
          <HomePosts />
          <HomeEmailSubscription />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  )
}
