import { AudioIcon } from '@/components/Icons/AudioIcon'
import { Text } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import { primary } from '@/styles/_colors'
import { LocalizeField } from '@/types/global.type'
import { Keyword } from '@/types/keyword.type'

import React from 'react'
import { ActivityIndicator, Pressable, TouchableOpacity, View } from 'react-native'

interface KeyworCardProps {
  keyword: Keyword
  onPlayAudio: (keyword: Keyword) => void
  isLoading?: boolean
  isPlaying?: boolean
  onOpenKeywordDetail?: (keyword: Keyword) => void
}

export default function KeyworCard({
  keyword,
  onPlayAudio,
  isLoading,
  isPlaying,
  onOpenKeywordDetail,
}: KeyworCardProps) {
  const { name, hiragana } = keyword

  const localizedName = name as unknown as LocalizeField<string>

  return (
    <Pressable
      className="w-[300px] overflow-hidden rounded-[8px] bg-[#EDF5FF] px-3 py-4"
      onPress={() => onOpenKeywordDetail?.(keyword)}
    >
      <View className="w-full flex-row items-start justify-between gap-2">
        <View className="flex-1 flex-col gap-2">
          <View className="flex-row items-center justify-between gap-3">
            <Text size="body2" className="line-clamp-2 " numberOfLines={2}>
              {localizedName[LocaleEnum.JA]}
            </Text>
          </View>
          {hiragana && (
            <Text size="body7" variant="subdued" className="line-clamp-2" numberOfLines={2}>
              /{hiragana}/
            </Text>
          )}
          <Text size="body7" variant="default" className="line-clamp-2" numberOfLines={2}>
            {localizedName[LocaleEnum.VI]}
          </Text>
        </View>
        <View className="mt-1 shrink-0">
          {isLoading ? (
            <ActivityIndicator size="small" color={primary[500]} />
          ) : (
            <TouchableOpacity
              className=" h-5 w-5  items-center justify-center p-0"
              onPress={(e) => {
                e.stopPropagation()
                onPlayAudio(keyword)
              }}
            >
              <AudioIcon width={20} height={20} isPlaying={isPlaying} />
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Pressable>
  )
}
