import DictionaryBookIcon from '@/assets/icons/dictionary-book-icon.svg'
import GalleryIcon from '@/assets/icons/gallery-icon.svg'
import MessageQuestionIcon from '@/assets/icons/message-question-icon.svg'
import TranslateIcon from '@/assets/icons/translate-icon.svg'
import { AudioIcon } from '@/components/Icons/AudioIcon'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { useSheetActions } from '@/contexts/SheetContext/SheetContext'
import { LocaleEnum } from '@/enums/locale.enum'
import { useGetKeywordAudio } from '@/hooks/query/keyword/useGetKeywordAudio'
import { useGetRandomKeywords } from '@/hooks/query/keyword/useGetRandomKeywords'
import { StyledExpoImage } from '@/libs/styled'
import { primary } from '@/styles/_colors'
import { LocalizeField } from '@/types/global.type'
import { Keyword } from '@/types/keyword.type'
import { Media } from '@/types/media.type'
import { useAudioPlayer, useAudioPlayerStatus } from 'expo-audio'
import { LinearGradient } from 'expo-linear-gradient'
import React, { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  ActivityIndicator,
  FlatList,
  ScrollView,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native'
import { ScrollView as GestureScrollView } from 'react-native-gesture-handler'
import RenderHTML from 'react-native-render-html'
import KeyworCard from './KeyworCard'

const TAGS_STYLES = {
  h1: {
    fontSize: 30,
    fontWeight: '700',
    marginBottom: 12,
  },
  h2: {
    fontSize: 24,
    fontWeight: '600',
    marginBottom: 10,
    marginTop: 16,
  },
  p: {
    fontSize: 16,
    lineHeight: 24,

    marginBottom: 12,
  },
  strong: {
    fontWeight: 'bold',
  },
  em: {
    fontStyle: 'italic',
  },
  a: {
    color: '#2563eb',
    textDecorationLine: 'underline',
  },
  ul: {
    paddingLeft: 20,
    marginBottom: 12,
  },
  ol: {
    paddingLeft: 20,
    marginBottom: 12,
  },
  li: {
    fontSize: 16,
    marginBottom: 6,
    lineHeight: 24,
  },
  blockquote: {
    fontStyle: 'italic',
    borderLeftWidth: 4,
    borderLeftColor: '#d1d5db',
    paddingLeft: 12,
    color: '#6b7280',
    marginBottom: 16,
  },
}

export const DailyVocabulary = () => {
  const { t } = useTranslation()

  const { randomKeywords, isGetRandomKeywordsLoading } = useGetRandomKeywords({
    params: {
      locale: 'all',
      limit: 5,
      depth: 5,
      convertDescriptionHTML: true,
      select: {
        name: true,
        id: true,
        hiragana: true,
        audio: true,
        relatedImages: true,
        description: true,
      },
    },
    useQueryOptions: {
      staleTime: 5 * 60 * 1000,
    },
  })

  const { getKeywordAudioMutation, isGetKeywordAudioPending } = useGetKeywordAudio()

  const [activeAudioUrl, setActiveAudioUrl] = useState<string | null>(null)
  const [activeKeywordId, setActiveKeywordId] = useState<string | null>(null)
  const [shouldPlay, setShouldPlay] = useState(false)
  const audioPlayer = useAudioPlayer(activeAudioUrl ?? undefined)
  const audioPlayerStatus = useAudioPlayerStatus(audioPlayer)

  const audioCache = useRef<Record<string, string>>({})
  const onPlayAudio = (keyword: Keyword) => {
    setActiveKeywordId(keyword.id)
    const existingUrl =
      audioCache.current[keyword.id] ||
      (keyword.audio?.find((item) => item.language === LocaleEnum.JA) as Media)?.url ||
      null

    if (existingUrl) {
      setActiveAudioUrl(existingUrl)
      setShouldPlay(true)
    } else {
      getKeywordAudioMutation(
        { keywordId: keyword.id, language: LocaleEnum.JA },
        {
          onSuccess: (data) => {
            if (data?.url) {
              audioCache.current[keyword.id] = data.url
              setActiveAudioUrl(data.url)
              setShouldPlay(true)
            }
          },
        },
      )
    }
  }

  useEffect(() => {
    if (audioPlayer && activeAudioUrl && shouldPlay) {
      audioPlayer.seekTo(0)
      audioPlayer.play()
      setShouldPlay(false)
    }
  }, [audioPlayer, activeAudioUrl, shouldPlay])

  const { openScrollSheet } = useSheetActions()
  const handleOpenKeywordDetail = (keyword: Keyword) => {
    openScrollSheet({
      children: <KeywordDetail keyword={keyword} />,
      baseProps: {
        snapPoints: ['50%', '90%'],
      },
    })
  }
  if (!isGetRandomKeywordsLoading && !randomKeywords?.docs.length) {
    return null
  }

  return (
    <View className="flex-col gap-5 px-4">
      <View className="flex-row items-center gap-2">
        <Text size="heading8" variant="primary">
          {t('MES-551')}
        </Text>
        <TranslateIcon width={24} height={24} />
      </View>
      {isGetRandomKeywordsLoading ? (
        <DailyVocabularyLoading />
      ) : (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ paddingRight: 16, gap: 16 }}
          decelerationRate="fast"
          snapToInterval={300 + 16}
        >
          {randomKeywords?.docs.map((keyword) => {
            const isPlaying =
              activeKeywordId === keyword.id &&
              audioPlayerStatus?.playing &&
              !isGetKeywordAudioPending &&
              !audioPlayerStatus?.didJustFinish
            const isLoading = isGetKeywordAudioPending && activeKeywordId === keyword.id
            return (
              <KeyworCard
                key={keyword.id}
                keyword={keyword}
                onPlayAudio={onPlayAudio}
                isLoading={isLoading}
                isPlaying={isPlaying}
                onOpenKeywordDetail={handleOpenKeywordDetail}
              />
            )
          })}
        </ScrollView>
      )}
    </View>
  )
}

const DailyVocabularyLoading = () => {
  return (
    <FlatList
      horizontal
      pagingEnabled
      data={new Array(4).fill(0)}
      keyExtractor={(_, index) => index.toString()}
      showsHorizontalScrollIndicator={false}
      decelerationRate="fast"
      snapToInterval={300 + 12}
      contentContainerStyle={{
        gap: 12,
      }}
      renderItem={({ index }) => (
        <Skeleton key={index} className="h-[120px] w-[300px] rounded-lg bg-gray-200" />
      )}
    />
  )
}

interface KeywordDetailProps {
  keyword: Keyword
}
const KeywordDetail = ({ keyword }: KeywordDetailProps) => {
  const { t } = useTranslation()
  const { name, hiragana, audio, relatedImages, description } = keyword
  const existingUrl = (audio?.find((item) => item.language === LocaleEnum.JA) as Media)?.url || null
  const localizedName = name as unknown as LocalizeField<string>
  const { width } = useWindowDimensions()
  const { getKeywordAudioMutation, isGetKeywordAudioPending } = useGetKeywordAudio()
  const [audioUrl, setAudioUrl] = useState<string | null>(existingUrl)
  const audioPlayer = useAudioPlayer(audioUrl ?? undefined)
  const audioPlayerStatus = useAudioPlayerStatus(audioPlayer)
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false)
  const [contentHeight, setContentHeight] = useState(0)
  const maxHeight = 200

  const handlePlayAudio = (keyword: Keyword) => {
    if (audioPlayer && audioUrl) {
      audioPlayer.seekTo(0)
      audioPlayer.play()
    } else {
      getKeywordAudioMutation(
        { keywordId: keyword.id, language: LocaleEnum.JA },
        {
          onSuccess: (data) => {
            setAudioUrl(data?.url)
          },
        },
      )
    }
  }
  useEffect(() => {
    if (audioPlayer && audioUrl) {
      audioPlayer.seekTo(0)
      audioPlayer.play()
    }
  }, [audioPlayer, audioUrl])
  return (
    <GestureScrollView
      contentContainerClassName="gap-3 bg-custom-background-hover p-4"
      className="flex-1"
    >
      {/* Translations */}
      <View className="flex flex-col gap-y-3 rounded-lg bg-white p-3">
        <View className="flex-row items-center  gap-x-2">
          <DictionaryBookIcon width={24} height={24} />
          <Text size="body6" variant="default">
            {t('MES-552')}
          </Text>
        </View>
        <View className="flex-row items-start justify-between gap-3 ">
          <View className="flex-1 flex-col gap-2 ">
            <View className="flex-row items-center justify-between gap-3">
              <Text size="heading7" variant="primary" className="line-clamp-1 block break-words">
                {localizedName[LocaleEnum.JA]}
              </Text>
            </View>
            {hiragana && (
              <Text size="body7" variant="subdued">
                /{hiragana}/
              </Text>
            )}
            <Text size="body7" variant="default">
              {localizedName[LocaleEnum.VI]}
            </Text>
          </View>
          <View className="mt-1 shrink-0">
            {isGetKeywordAudioPending ? (
              <ActivityIndicator size="small" color={primary[500]} />
            ) : (
              <TouchableOpacity
                className="h-5 w-5 shrink-0 items-center justify-center p-0"
                onPress={(e) => {
                  e.stopPropagation()
                  if (audioPlayerStatus?.playing) {
                    audioPlayer.pause()
                    audioPlayer.seekTo(0)
                    audioPlayer.play()
                  } else {
                    handlePlayAudio(keyword)
                  }
                }}
              >
                <AudioIcon width={20} height={20} isPlaying={audioPlayerStatus?.playing} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
      {/* Description */}

      {description && (
        <View className="flex flex-col gap-y-3 rounded-lg bg-white p-3">
          <View className="flex-row items-center  gap-x-2">
            <MessageQuestionIcon width={24} height={24} />
            <Text size="body6" variant="default">
              {t('MES-553')}
            </Text>
          </View>
          <View className="relative overflow-hidden pt-2">
            <View style={{ maxHeight: isDescriptionExpanded ? undefined : maxHeight }}>
              <View
                onLayout={(event) => {
                  const { height } = event.nativeEvent.layout
                  setContentHeight(height)
                }}
              >
                <RenderHTML
                  source={{ html: description as unknown as string }}
                  contentWidth={width}
                  tagsStyles={TAGS_STYLES as unknown as Record<string, any>}
                  baseStyle={{
                    marginTop: -8,
                  }}
                />
              </View>
            </View>

            {!isDescriptionExpanded && contentHeight > maxHeight && (
              <LinearGradient
                colors={['rgba(255,255,255,0)', 'rgba(255,255,255,0.9)', 'rgba(255,255,255,1)']}
                style={{
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  right: 0,
                  height: 60,
                  zIndex: 5,
                }}
              />
            )}
          </View>

          {contentHeight > maxHeight && (
            <TouchableOpacity
              onPress={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
              className="sell-start rounded-full py-1"
            >
              <Text size="link3" variant="primary" className="text-center">
                {isDescriptionExpanded ? t('MES-595') : t('MES-594')}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}

      {/* Related Images */}
      {relatedImages?.length && (
        <View className="flex flex-col gap-y-3">
          <View className="flex-row items-center gap-x-2">
            <GalleryIcon width={24} height={24} />
            <Text size="body6" variant="default">
              {t('MES-573')}
            </Text>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ gap: 12 }}
            className="bg-white p-3"
            pagingEnabled
            snapToInterval={240 + 12}
            decelerationRate={'fast'}
          >
            {relatedImages?.map((image, index) => {
              const media = image as Media
              const imageUrl = media?.thumbnailURL || media?.url || ''
              return imageUrl ? (
                <StyledExpoImage
                  key={index}
                  source={{ uri: imageUrl }}
                  className="aspect-video  w-[240px] rounded-lg"
                  contentFit="cover"
                />
              ) : null
            })}
          </ScrollView>
        </View>
      )}
    </GestureScrollView>
  )
}
