import Translate2Icon from '@/assets/icons/translate-2-icon.svg'
import { HOME_FEATURED_LIST } from '@/constants/navigation.constant'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Text, View } from 'react-native'
import { FeatureCard } from './FeatureCard'

export const FeatureList = () => {
  const { t } = useTranslation()
  return (
    <View className="flex-col gap-4 px-4">
      <View className="flex-row items-center gap-2">
        <Text className="text-lg font-semibold leading-[18px] text-primary">{t('MES-640')}</Text>
        <Translate2Icon width={24} height={24} />
      </View>

      <View className="flex-row justify-between gap-2">
        {HOME_FEATURED_LIST.map((item, index) => (
          <FeatureCard key={index} url={item.url} icon={item?.icon} title={t(item.title)} />
        ))}
      </View>
    </View>
  )
}
