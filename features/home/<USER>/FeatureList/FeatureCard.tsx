import { Text } from '@/components/ui/Text/Text'
import { Link, LinkProps } from 'expo-router'
import React from 'react'
import { TouchableOpacity, View } from 'react-native'
import { SvgProps } from 'react-native-svg'

interface FeatureCardProps {
  icon: React.ComponentType<SvgProps>
  title: string
  url: string
}

export const FeatureCard = ({ icon: IconComponent, title, url }: FeatureCardProps) => {
  return (
    <Link href={url as LinkProps['href']} asChild>
      <TouchableOpacity className="flex-1 items-center justify-center gap-2 rounded-[8px] border border-custom-neutral-50 bg-white p-3">
        <View className="flex-col items-center justify-center gap-2">
          <View className="h-[45px] w-[45px] items-center justify-center rounded-[6px] bg-primary-50">
            <IconComponent width={28} height={28} />
          </View>

          <Text size="body8" className="line-clamp-2 h-10 text-center" numberOfLines={2}>
            {title}
          </Text>
        </View>
      </TouchableOpacity>
    </Link>
  )
}
