import NewspaperIcon from '@/assets/icons/newspaper-icon.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/Tabs/Tabs'
import { Text } from '@/components/ui/Text/Text'
import { BLURHASH_CODE } from '@/constants/global.constant'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useGetPostCategories } from '@/hooks/query/post/useGetPostCategories'
import { useGetPosts } from '@/hooks/query/post/useGetPosts'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES } from '@/routes/appRoutes'
import { Media } from '@/types/media.type'
// Correct the import path for PostCategory
import { Post, PostCategory } from '@/types/post.type'
import { cn } from '@/utils/cn'
import dayjs from 'dayjs'
import { Link, LinkProps } from 'expo-router'
import React, { useCallback, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { FlatList, ScrollView, TouchableOpacity, View } from 'react-native'
const DEFAULT_POST_CATEGORY = 'ALL'
export const HomePosts = () => {
  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()
  const [selectedCategory, setSelectedCategory] = useState(DEFAULT_POST_CATEGORY)

  const { postCategories, isGetPostCategoriesLoading } = useGetPostCategories({
    params: {
      locale: primaryLanguage,
      limit: 10,
      depth: 5,
    },
  })
  // Create where condition for posts query
  const whereCondition = useMemo(() => {
    return {
      language: { equals: primaryLanguage as LocaleEnum },
      featured: { equals: true },
      ...(selectedCategory && selectedCategory !== DEFAULT_POST_CATEGORY
        ? { 'categories.id': { equals: selectedCategory } }
        : {}),
    }
  }, [primaryLanguage, selectedCategory])
  const { posts, isGetPostsLoading } = useGetPosts({
    params: {
      limit: 17,
      where: whereCondition,
      depth: 1,
      locale: (primaryLanguage as LocaleEnum) || 'vi',
      draft: false,
      sort: '-createdAt',
      select: {
        title: true,
        id: true,
        heroImage: true,
        categories: true,
        createdAt: true,
        slug: true,
      },
    },
    useQueryOptions: {
      staleTime: 0,
    },
  })

  // Get the first post (highlighted)
  const firstPost = useMemo(() => posts?.docs?.[0], [posts])
  const prepareSlides = useCallback(() => {
    if (!posts?.docs || posts.docs.length <= 1) return []

    const slides: Post[][] = []
    const totalPosts = posts.docs.length

    // Skip the first post
    for (let i = 1; i < totalPosts; i += 4) {
      const slidePostsCount = Math.min(4, totalPosts - i)
      if (slidePostsCount > 0) {
        slides.push(posts.docs.slice(i, i + 4))
      }
    }

    return slides
  }, [posts])
  const postsSlides = useMemo(() => prepareSlides(), [prepareSlides])

  const tabTriggerClass = useMemo(() => {
    return {
      default:
        'self-start min-w-[67px] h- border border-transparent rounded-[60px] overflow-hidden bg-custom-neutral-80 px-3 py-1 text-center',
      active: 'border border-primary bg-white text-primary',
      'text-default': 'text-custom-text-subdued text-center',
      'text-active': 'text-primary-500',
    }
  }, [])
  return (
    <View className="flex-1 px-4">
      <View className="mb-3 flex-row items-center gap-3">
        <Text size="heading8" variant="primary">
          {t('MES-548')}
        </Text>
        <NewspaperIcon className="h-6 w-6" />
      </View>
      <Tabs
        value={selectedCategory}
        onValueChange={(value) => {
          setSelectedCategory(value)
        }}
      >
        {/* Update Tabs to represent categories */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <TabsList className="flex-1 flex-row gap-x-2 self-start">
            {/* ALL CATEGORY */}
            <TabsTrigger
              value={DEFAULT_POST_CATEGORY}
              isActive={selectedCategory === DEFAULT_POST_CATEGORY}
            >
              <View
                className={cn(
                  tabTriggerClass['default'],
                  selectedCategory === DEFAULT_POST_CATEGORY &&
                    'border !border-primary !bg-white text-primary',
                )}
              >
                <Text
                  size="body6"
                  className={cn(
                    tabTriggerClass['text-default'],
                    selectedCategory === DEFAULT_POST_CATEGORY && tabTriggerClass['text-active'],
                  )}
                >
                  {t('MES-141')}
                </Text>
              </View>
            </TabsTrigger>
            {/* CATEGORIES */}
            {!isGetPostCategoriesLoading &&
              postCategories?.docs?.map((category: PostCategory) => (
                <TabsTrigger
                  key={category.id}
                  value={category.id}
                  isActive={selectedCategory === category.id}
                >
                  <View
                    className={cn(
                      tabTriggerClass['default'],
                      selectedCategory === category.id &&
                        'border !border-primary !bg-white text-primary',
                    )}
                  >
                    <Text
                      size="body6"
                      className={cn(
                        tabTriggerClass['text-default'],
                        selectedCategory === category.id && tabTriggerClass['text-active'],
                      )}
                    >
                      {category.title}
                    </Text>
                  </View>
                </TabsTrigger>
              ))}
          </TabsList>
        </ScrollView>

        <TabsContent value={DEFAULT_POST_CATEGORY} className="mt-4">
          <HomePostList firstPost={firstPost} posts={postsSlides} isLoading={isGetPostsLoading} />
        </TabsContent>

        {postCategories?.docs?.map((category: PostCategory) => (
          <TabsContent key={category.id} value={category.id} className="mt-4">
            <HomePostList firstPost={firstPost} posts={postsSlides} isLoading={isGetPostsLoading} />
          </TabsContent>
        ))}
      </Tabs>
    </View>
  )
}

const HomePostsLoading = () => {
  return (
    <FlatList
      horizontal
      pagingEnabled
      data={Array.from({ length: 4 }, () => new Array(4).fill(0))}
      showsHorizontalScrollIndicator={false}
      decelerationRate={'fast'}
      snapToInterval={250 + 12}
      contentContainerStyle={{
        gap: 12,
      }}
      renderItem={({ item }) => (
        <View className="flex-1">
          <View className="flex-col gap-3">
            {item.map((_, index) => {
              return <Skeleton key={index} className="h-[80px] w-[250px] rounded-lg bg-gray-200" />
            })}
          </View>
        </View>
      )}
    />
  )
}

interface HomePostListProps {
  posts: Post[][]
  isLoading: boolean
  firstPost?: Post | null
}
const HomePostList = ({ posts, isLoading, firstPost }: HomePostListProps) => {
  const firstPostCategory = (firstPost?.categories as PostCategory)?.title
  const firsPostImageURL =
    (firstPost?.heroImage as Media)?.url || (firstPost?.heroImage as Media)?.thumbnailURL || ''
  return (
    <>
      {!isLoading ? (
        <>
          {firstPost && (
            <Link
              href={
                {
                  pathname: APP_ROUTES.POSTS.path + '/[slug]',
                  params: { slug: firstPost.slug },
                } as LinkProps['href']
              }
              className="mb-4 w-full"
              asChild
            >
              <TouchableOpacity className=" w-full overflow-hidden">
                <StyledExpoImage
                  source={firsPostImageURL}
                  contentFit="cover"
                  transition={300}
                  placeholder={BLURHASH_CODE}
                  className="mb-3 aspect-video w-full"
                />
                <Text
                  size="body8"
                  variant="primary"
                  className="mb-2 h-6 self-start rounded-[4px] bg-primary-50 px-2 py-1"
                >
                  {firstPostCategory}
                </Text>
                <Text size="body9" variant="subdued" className="mb-2">
                  {dayjs(firstPost?.createdAt).format('YYYY/MM/DD')}
                </Text>
                <Text size="body6" className="uppercase">
                  {firstPost?.title}
                </Text>
              </TouchableOpacity>
            </Link>
          )}
          <FlatList
            horizontal
            pagingEnabled
            data={posts}
            renderItem={({ item }) => (
              <View className="w-[300px] flex-1 flex-col gap-3 overflow-hidden rounded-lg bg-custom-neutral-50 p-2">
                {item.map((post, index, array) => {
                  const thumbnail = post.heroImage as Media
                  const thumbnailUrl = thumbnail?.url || thumbnail?.thumbnailURL || ''
                  return (
                    <Link
                      key={`${post.id}-${index}`}
                      href={
                        {
                          pathname: APP_ROUTES.POSTS.path + '/[slug]',
                          params: { slug: post.slug },
                        } as LinkProps['href']
                      }
                      className="flex w-full"
                      asChild
                    >
                      <TouchableOpacity
                        className={cn(
                          ' h-[88px] w-full flex-row gap-3 overflow-hidden border-b border-custom-divider p-2',
                          array.length - 1 === index && 'border-b-0',
                        )}
                      >
                        <View className="aspect-video h-[72px]  overflow-hidden">
                          {thumbnailUrl && (
                            <StyledExpoImage
                              source={thumbnailUrl}
                              contentFit="cover"
                              transition={300}
                              placeholder={BLURHASH_CODE}
                              className="h-full w-full"
                            />
                          )}
                        </View>
                        <View className="w-full flex-1 flex-col gap-[6px] overflow-hidden">
                          <Text size="body9" variant="subdued">
                            {dayjs(post.createdAt).format('YYYY/MM/DD') || 'No date'}
                          </Text>
                          <Text
                            size="body8"
                            className=" w-full "
                            numberOfLines={2}
                            ellipsizeMode="tail"
                          >
                            {post.title || 'No title'}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    </Link>
                  )
                })}
              </View>
            )}
            snapToInterval={300 + 12}
            decelerationRate={'fast'}
            contentContainerStyle={{
              gap: 12,
            }}
            showsHorizontalScrollIndicator={false}
          />
        </>
      ) : (
        <HomePostsLoading />
      )}
    </>
  )
}
