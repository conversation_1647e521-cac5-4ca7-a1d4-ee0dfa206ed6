import ArrowDownIcon from '@/assets/icons/arrow-down-icon.svg'
import SearchInputIcon from '@/assets/icons/search-input-icon.svg'
import SearchStethoscopeIcon from '@/assets/icons/search-stethoscope-icon.svg'
import StarIcon from '@/assets/icons/start-icon.svg'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/Accordion/Accordion'
import { Text } from '@/components/ui/Text/Text'
import { useSheetActions } from '@/contexts/SheetContext/SheetContext'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useGetInfiniteKeywordsV2 } from '@/hooks/query/keyword/useGetInfiniteKeywordsV2'
import { APP_ROUTES } from '@/routes/appRoutes'
import { LocalizeField } from '@/types/global.type'
import { Keyword } from '@/types/keyword.type'
import { cn } from '@/utils/cn'
import { BottomSheetFlatList, BottomSheetTextInput } from '@gorhom/bottom-sheet'
import { LinearGradient } from 'expo-linear-gradient'
import { LinkProps, useRouter } from 'expo-router'
import { debounce } from 'lodash-es'
import { stringify } from 'qs-esm'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  ActivityIndicator,
  ListRenderItem,
  Platform,
  ScrollView,
  StyleSheet,
  TouchableHighlight,
  TouchableOpacity,
  View,
} from 'react-native'
import { TextInput as GestureTextInput } from 'react-native-gesture-handler'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { useGetRandomSearchTips } from '../../hooks/query/search-tip/useGetRandomSearchTips'

type KeywordItem = Pick<Keyword, 'id' | 'name'>

export const HomeSearch = () => {
  const { t } = useTranslation()
  const { openCustomSheet } = useSheetActions()
  const searchInputRef = useRef<GestureTextInput>(null)
  const shouldFoccus = useRef(true)
  const handleOpenKeywordSheet = useCallback(() => {
    openCustomSheet({
      children: <KeywordSheet searchInputRef={searchInputRef} />,
      baseProps: {
        snapPoints: ['50%', '70%'],
        enableDynamicSizing: false,
        enableOverDrag: false,
        // onClose,
      },
      options: {
        // Only focus input when sheet is opened for the first time
        onChange: (index, _) => {
          if (index === 0 && shouldFoccus.current) {
            searchInputRef.current?.focus()
          }
          if (index === 1) {
            shouldFoccus.current = false
          }
        },
        // Reset shouldFocus when sheet is closed
        onClose: () => {
          shouldFoccus.current = true
        },
      },
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [t])

  return (
    <View className="mt-3 flex px-4 ">
      <LinearGradient
        colors={['#5BAEF3', '#1764E0']}
        start={{ x: 0.999, y: 1.046 }}
        end={{ x: 0.02, y: 0.085 }}
        style={{
          paddingHorizontal: 16,
          paddingVertical: 24,
          borderRadius: 8,
          overflow: 'hidden',
          borderBottomLeftRadius: 0,
          borderBottomRightRadius: 0,
        }}
      >
        {/* Decorative circles */}
        <View style={styles.decorativeCircle1} />
        <View style={styles.decorativeCircle2} />
        <View style={styles.decorativeCircle3} />

        <View className="flex flex-row items-center justify-between gap-x-2">
          <Text size="heading7" variant="white" className="max-w-[244px] flex-1">
            {t('MES-554')}
          </Text>

          {/* Stethoscope illustration */}
          <View className="shrink-0">
            <SearchStethoscopeIcon />
          </View>
        </View>

        {/* Search Bar */}
        <TouchableHighlight
          onPress={handleOpenKeywordSheet}
          underlayColor="transparent"
          accessibilityRole="button"
        >
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              gap: 8,
              backgroundColor: 'white',

              borderRadius: 8,
              marginTop: 12,
              overflow: 'hidden',
              padding: 12,
            }}
          >
            <SearchInputIcon width={18} height={18} />

            <View className="flex-1 overflow-hidden">
              <Text className="line-clamp-1" size="field1" variant="subdued">
                {t('MES-562')}
              </Text>
            </View>
          </View>
        </TouchableHighlight>
      </LinearGradient>

      <HomeTips />
    </View>
  )
}

interface KeywordSheetProps {
  searchInputRef: React.RefObject<GestureTextInput | null>
}

const KeywordSheet = ({ searchInputRef }: KeywordSheetProps) => {
  const { primaryLanguage, secondaryLanguage } = useAppLanguage()
  const { closeSheet } = useSheetActions()
  const router = useRouter()
  const insets = useSafeAreaInsets()
  const { t } = useTranslation()
  const [searchKeywordValue, setSearchKeywordValue] = useState('')
  const [searchInputValue, setSearchInputValue] = useState('')

  const { keywords, isGetKeywordsLoading, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useGetInfiniteKeywordsV2({
      params: {
        locale: 'all',
        limit: 20,
        searchValue: searchKeywordValue || '',
      },
      config: {
        staleTime: 5 * 60 * 1000,
      },
    })

  // Create debounced search function
  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchKeywordValue(value)
      }, 300),
    [],
  )
  useEffect(() => {
    return () => {
      debouncedSearch.cancel()
    }
  }, [debouncedSearch])
  // Handle input change with debounce
  const handleSearchInputChange = useCallback(
    (text: string) => {
      setSearchInputValue(text)
      debouncedSearch(text)
    },
    [debouncedSearch],
  )

  const flatKeywordItems = keywords?.pages.flatMap((page) => page.docs) || []
  const keyExtractor = useCallback((item: unknown) => (item as KeywordItem)?.id, [])
  const renderKeywordItem: ListRenderItem<KeywordItem> = useCallback(({ item }) => {
    const localizedName = item?.name as unknown as LocalizeField<string>
    const keywordParamFormat = stringify({
      q:
        `${localizedName[primaryLanguage as LocaleEnum]}` +
        (secondaryLanguage ? ` (${localizedName[secondaryLanguage as LocaleEnum]})` : ''),
    })
    return (
      <TouchableOpacity
        className="border-b border-custom-divider px-4 py-2"
        onPress={() => {
          closeSheet()
          router.push({
            pathname: APP_ROUTES.SEARCH_SUMMARY.path,
            params: {
              keyword: keywordParamFormat,
            },
          } as LinkProps['href'])
        }}
      >
        {localizedName[primaryLanguage as LocaleEnum] && (
          <Text size="body6">{localizedName[primaryLanguage as LocaleEnum]}</Text>
        )}
        {localizedName[secondaryLanguage as LocaleEnum] && (
          <Text size="body6" variant="subdued">
            {localizedName[secondaryLanguage as LocaleEnum]}
          </Text>
        )}
      </TouchableOpacity>
    )
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  const handleEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage()
    }
  }, [fetchNextPage, hasNextPage, isFetchingNextPage])
  return (
    <View style={{ paddingBottom: insets.bottom }}>
      <View className="flex-col gap-y-2 pb-3">
        <View className="mx-4 flex-row items-center gap-x-2 overflow-hidden rounded-lg border border-custom-divider bg-white p-3">
          <SearchInputIcon width={18} height={18} />
          <BottomSheetTextInput
            placeholder={t('MES-562')}
            placeholderTextColor="#8B8C99"
            value={searchInputValue}
            onChangeText={handleSearchInputChange}
            style={{ flex: 1 }}
            className="p-0"
            ref={searchInputRef}
            onSubmitEditing={(e) => {
              const keywordParamFormat = stringify({
                q: e.nativeEvent.text,
              })

              closeSheet()
              router.push({
                pathname: APP_ROUTES.SEARCH_SUMMARY.path,
                params: {
                  keyword: keywordParamFormat,
                },
              } as LinkProps['href'])
            }}
          />
        </View>
      </View>
      {isGetKeywordsLoading && !isFetchingNextPage ? (
        <View className="items-center py-4">
          <ActivityIndicator size="small" />
        </View>
      ) : (
        <BottomSheetFlatList
          contentContainerStyle={{ paddingBottom: insets.bottom + 16 }}
          renderItem={renderKeywordItem}
          data={flatKeywordItems}
          keyExtractor={keyExtractor}
          onEndReached={handleEndReached}
          onEndReachedThreshold={0.8}
          initialNumToRender={20}
          maxToRenderPerBatch={20}
          windowSize={21}
          removeClippedSubviews={true}
          ListFooterComponent={() => {
            return isFetchingNextPage ? (
              <View className="items-center py-4">
                <ActivityIndicator />
              </View>
            ) : null
          }}
          ListEmptyComponent={() => {
            return !isGetKeywordsLoading && flatKeywordItems.length === 0 ? (
              <View className="items-center py-10">
                <Text size="body6">{t('MES-517')}</Text>
              </View>
            ) : null
          }}
        />
      )}
    </View>
  )
}

const HomeTips = () => {
  const { primaryLanguage } = useAppLanguage()
  const { t } = useTranslation()
  const { randomSearchTips, isGetRandomSearchTipsLoading } = useGetRandomSearchTips({
    params: {
      locale: primaryLanguage,
      limit: 5,
    },
    useQueryOptions: {
      staleTime: 5 * 60 * 1000,
    },
  })
  return (
    <Accordion className="w-full rounded-b-lg" type="multiple">
      <AccordionItem
        value="1"
        className={cn(
          'w-full rounded-b-lg ',

          Platform.OS === 'android' && 'border border-t-0 border-custom-divider',
        )}
        isDisabled={isGetRandomSearchTipsLoading || !randomSearchTips?.docs.length}
      >
        <AccordionTrigger className="w-full  ">
          <View className="w-full  flex-row items-center justify-between ">
            <View className="flex-row items-center gap-x-2">
              <Text size="body6" variant="primary">
                {t('MES-555')}
              </Text>
              <StarIcon />
            </View>
            <ArrowDownIcon width={18} height={18} />
          </View>
        </AccordionTrigger>
        <AccordionContent className="rounded-b-lg ">
          <ScrollView
            horizontal
            className=""
            pagingEnabled
            contentContainerStyle={{ gap: 12 }}
            snapToInterval={272 + 12}
            showsHorizontalScrollIndicator={false}
            decelerationRate={'fast'}
          >
            {randomSearchTips?.docs.map((searchTip) => (
              <View
                key={searchTip.id}
                className="w-[272px] flex-col gap-2 rounded-lg border border-custom-divider p-3"
              >
                <Text size="body6">{searchTip.title}</Text>
                <Text size="body7" variant="subdued">
                  {searchTip.description}
                </Text>
              </View>
            ))}
          </ScrollView>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  )
}

const styles = StyleSheet.create({
  decorativeCircle1: {
    position: 'absolute',
    width: 264.34,
    height: 264.34,
    left: -122,
    top: -92,
    borderWidth: 1,
    borderColor: 'rgba(141, 238, 230, 0.2)',
    borderRadius: 132.17,
    backgroundColor: 'transparent',
  },
  decorativeCircle2: {
    position: 'absolute',
    width: 95.02,
    height: 95.02,
    right: -47.51,
    top: 102,
    borderWidth: 1,
    borderColor: 'rgba(141, 238, 230, 0.1)',
    borderRadius: 47.51,
    backgroundColor: 'transparent',
  },
  decorativeCircle3: {
    position: 'absolute',
    width: 64,
    height: 64,
    left: 67,
    top: -40,
    borderRadius: 32,
    backgroundColor: 'rgba(141, 238, 230, 0.1)',
  },
})
