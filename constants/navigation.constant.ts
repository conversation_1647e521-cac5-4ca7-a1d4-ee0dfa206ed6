import CubeScanIcon from '@/assets/icons/3d-cube-scan-icon.svg'
import BodyIcon from '@/assets/icons/body-icon.svg'
import Edit2Icon from '@/assets/icons/edit-2-icon.svg'
import HospitalIcon from '@/assets/icons/hospital-icon.svg'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'

import DictionaryIcon from '@/assets/icons/dictionary-icon.svg'
import HealthIcon from '@/assets/icons/health-icon-full.svg'
import HomeIcon from '@/assets/icons/home-icon.svg'
import MedicineIcon from '@/assets/icons/medicine-icon-full.svg'
import NewsIcon from '@/assets/icons/news-icon-full.svg'
import { SvgProps } from 'react-native-svg'
export interface HomeFeaturedCategory {
  title: string
  icon: React.ComponentType<SvgProps>
  url: string
}

export const APP_TABS = {
  [AppRoutesEnum.MEDICAL_DICTIONARY]: {
    name: APP_ROUTES.MEDICAL_DICTIONARY.tabName,
    path: APP_ROUTES.MEDICAL_DICTIONARY.path,
    keyTranslate: 'MES-644',
    icon: DictionaryIcon,
  },
  [AppRoutesEnum.MEDICAL_HANDBOOK]: {
    name: APP_ROUTES.MEDICAL_HANDBOOK.tabName,
    path: APP_ROUTES.MEDICAL_HANDBOOK.path,
    keyTranslate: 'MES-33',
    icon: HealthIcon,
  },
  [AppRoutesEnum.HOME]: {
    name: APP_ROUTES.HOME.name,
    path: APP_ROUTES.HOME.path,
    keyTranslate: 'MES-37',
    icon: HomeIcon,
  },
  [AppRoutesEnum.PRODUCTS]: {
    name: APP_ROUTES.PRODUCTS.tabName,
    path: APP_ROUTES.PRODUCTS.path,
    keyTranslate: 'MES-586',
    icon: MedicineIcon,
  },
  [AppRoutesEnum.POSTS]: {
    name: APP_ROUTES.POSTS.tabName,
    path: APP_ROUTES.POSTS.path,
    keyTranslate: 'MES-19',
    icon: NewsIcon,
  },
}

export const HOME_FEATURED_LIST: HomeFeaturedCategory[] = [
  {
    title: 'MES-641',
    icon: HospitalIcon,
    url: APP_ROUTES.MEDICAL_HANDBOOK.tabPath,
  },
  {
    title: 'MES-36',
    icon: BodyIcon,
    url: APP_ROUTES.BODY_PARTS?.path,
  },
  {
    title: 'MES-642',
    icon: CubeScanIcon,
    url: APP_ROUTES.CHAT_BOT?.children?.CHAT_BOT_SEARCH_MEDICINE?.path,
  },
  {
    title: 'MES-643',
    icon: Edit2Icon,
    url: APP_ROUTES.EXAMINATION?.path,
  },
]
