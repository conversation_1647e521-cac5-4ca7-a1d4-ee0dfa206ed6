import { SESSION_EXPIRED_EVENT_NAME } from '@/constants/event.constant'
import { ACCESS_TOKEN, MAX_AGE_TOKEN, X_LOGIN_SESSION } from '@/constants/storage-key.constant'
import { authService } from '@/services/auth/auth.service'
import { secureStoreService } from '@/services/secure-store/secure-store.service'
import { LoginResponse, RefreshTokenResponse } from '@/types/auth.type'
import { User } from '@/types/user.type'
import dayjs from 'dayjs'
import { produce } from 'immer'
import { createContext, useEffect, useRef, useState } from 'react'
import { DeviceEventEmitter } from 'react-native'

export type AuthenticationStatus = 'loading' | 'success' | 'unauthorized' | 'failure'

export interface AuthenticationState {
  user?: User
  tokenExp?: number
  status: AuthenticationStatus
  update: (status?: AuthenticationStatus) => void
  signout: ({ showLoading, showToast }: { showLoading?: boolean; showToast?: boolean }) => void
  fetchCurrentUser: ({
    setStatusLoading,
  }: {
    setStatusLoading?: boolean
  }) => Promise<User | null | undefined>
  refreshToken: () => Promise<RefreshTokenResponse | undefined>
  login: (email: string, password: string, lang?: string) => Promise<LoginResponse>
  googleLogin: (idToken: string) => Promise<any>
  logout: () => Promise<void>
}

export const defaultValues: AuthenticationState = {
  status: 'loading',
  user: undefined,
  tokenExp: undefined,
  update: () => {},
  signout: () => {},
  fetchCurrentUser: async () => null,
  refreshToken: async () => undefined,
  login: async () => ({}) as LoginResponse,
  googleLogin: async () => ({}),
  logout: async () => {},
}

export const AuthenticationContext = createContext<AuthenticationState>(defaultValues)

export const AuthenticationProvider: React.FC<{
  children: React.ReactNode
}> = ({ children }) => {
  const tokenRefreshTimeoutRef = useRef<number | null>(null)
  const currentTokenExpRef = useRef<number | undefined>(undefined)

  const scheduleTokenRefresh = (exp: number) => {
    // Clear any existing timeout first
    if (tokenRefreshTimeoutRef.current) {
      clearTimeout(tokenRefreshTimeoutRef.current)
      tokenRefreshTimeoutRef.current = null
    }

    const expirationTime = dayjs.unix(exp)
    const refreshTime = expirationTime.subtract(5, 'minute')
    const timeUntilRefresh = refreshTime.diff(dayjs(), 'millisecond')

    if (timeUntilRefresh > 0) {
      tokenRefreshTimeoutRef.current = setTimeout(async () => {
        try {
          await authState.refreshToken()
        } catch (error) {
          console.error('Failed to refresh token:', error)
        }
      }, timeUntilRefresh)
    } else if (timeUntilRefresh <= 0 && expirationTime.diff(dayjs(), 'millisecond') > 0) {
      // If we're within 5 minutes of expiration but token is still valid, refresh immediately
      ;(async () => {
        try {
          await authState.refreshToken()
        } catch (error) {
          console.error('Failed to refresh token:', error)
        }
      })()
    }
  }

  const clearTokenRefreshSchedule = () => {
    if (tokenRefreshTimeoutRef.current) {
      clearTimeout(tokenRefreshTimeoutRef.current)
      tokenRefreshTimeoutRef.current = null
    }
  }

  const [authState, setAuthState] = useState<AuthenticationState>({
    status: 'loading',
    user: undefined,
    tokenExp: undefined,
    update: (status?: AuthenticationStatus) => {
      setAuthState(
        produce((draft) => {
          // Store previous status to check for transitions
          const previousStatus = draft.status
          draft.status = status || 'loading'

          // Only clear token refresh when transitioning from success to unauthorized
          if (previousStatus === 'success' && status === 'unauthorized') {
            draft.user = undefined
            draft.tokenExp = undefined
            clearTokenRefreshSchedule()
          }
        }),
      )
    },
    signout: async ({ showLoading = true, showToast = true } = {}) => {
      try {
        if (showLoading) {
          console.log('Signing out...')
        }

        // Clear token refresh schedule - this is appropriate during explicit signout
        clearTokenRefreshSchedule()

        // Clear secure storage
        await secureStoreService.removeItem(ACCESS_TOKEN)
        await secureStoreService.removeItem(X_LOGIN_SESSION)
        await secureStoreService.removeItem(MAX_AGE_TOKEN)
        setAuthState(
          produce((draft) => {
            draft.user = undefined
            draft.tokenExp = undefined
            draft.status = 'unauthorized'
          }),
        )

        if (showToast) {
          console.log('Successfully signed out')
        }
      } catch (error) {
        console.error('Failed to sign out:', error)
      }
    },
    refreshToken: async () => {
      try {
        const result = await authService.refreshToken({})
        if (result?.user && result?.exp) {
          setAuthState(
            produce((draft) => {
              draft.user = result.user
              draft.tokenExp = result.exp
              draft.status = 'success'
            }),
          )

          // Update the current token expiration reference
          currentTokenExpRef.current = result.exp
          await secureStoreService.setItem(MAX_AGE_TOKEN, result.exp.toString())
          // Reschedule the next token refresh based on the new token's expiration
          scheduleTokenRefresh(result.exp)
        }
        return result
      } catch (error) {
        console.error('Failed to refresh token:', error)
        throw error
      }
    },
    fetchCurrentUser: async ({ setStatusLoading } = {}) => {
      if (setStatusLoading) {
        setAuthState(
          produce((draft) => {
            draft.status = 'loading'
          }),
        )
      }

      try {
        const res = await authService.getCurrentUser()

        // Update state based on user response
        setAuthState(
          produce((draft) => {
            draft.user = res?.user || undefined
            draft.tokenExp = res?.exp
            draft.status = res?.user ? 'success' : 'unauthorized'
          }),
        )

        if (res?.exp) {
          currentTokenExpRef.current = res.exp
        }

        return res?.user || null
      } catch (error) {
        console.error('Failed to fetch user:', error)
        setAuthState(
          produce((draft) => {
            draft.user = undefined
            draft.tokenExp = undefined
            draft.status = 'failure'
          }),
        )
        return null
      }
    },
    login: async (email: string, password: string, lang?: string) => {
      try {
        const result = await authService.login(email, password, lang)

        setAuthState(
          produce((draft) => {
            draft.user = result.user
            draft.tokenExp = result.exp
            draft.status = 'success'
          }),
        )

        // Update the current token expiration reference
        currentTokenExpRef.current = result.exp

        // Schedule token refresh
        if (result.exp) {
          scheduleTokenRefresh(result.exp)
        }

        return result
      } catch (error) {
        console.error('Login failed:', error)
        throw error
      }
    },
    googleLogin: async (idToken: string) => {
      try {
        const result = await authService.googleLogin(idToken)
        if (result?.exp) {
          currentTokenExpRef.current = result.exp
          await secureStoreService.setItem(MAX_AGE_TOKEN, result.exp.toString())

          const userResponse = await authService.getCurrentUser()

          setAuthState(
            produce((draft) => {
              draft.user = userResponse?.user || undefined
              draft.tokenExp = result.exp
              draft.status = userResponse?.user ? 'success' : 'unauthorized'
            }),
          )

          // Schedule token refresh with the Google login exp
          scheduleTokenRefresh(result.exp)
        }
        return result
      } catch (error) {
        console.error('Google login failed:', error)
        throw error
      }
    },
    logout: async () => {
      await authService.logout({})
      await secureStoreService.removeItem(ACCESS_TOKEN)
      await secureStoreService.removeItem(X_LOGIN_SESSION)
      await secureStoreService.removeItem(MAX_AGE_TOKEN)
      clearTokenRefreshSchedule()
      setAuthState(
        produce((draft) => {
          draft.user = undefined
          draft.tokenExp = undefined
          draft.status = 'unauthorized'
        }),
      )
    },
  })

  // Initialize the token expiration on mount
  useEffect(() => {
    const initAuth = async () => {
      const response = await authState.fetchCurrentUser({})
      if (response && authState.tokenExp) {
        currentTokenExpRef.current = authState.tokenExp
      }
    }
    initAuth()
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Schedule token refresh when status changes to success
  useEffect(() => {
    if (authState.status === 'success' && currentTokenExpRef.current) {
      scheduleTokenRefresh(currentTokenExpRef.current)

      return () => {
        clearTokenRefreshSchedule()
      }
    }
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authState.status])

  // Listen for session expiration events from the HTTP service
  useEffect(() => {
    const handleSessionExpired = () => {
      if (authState.status === 'success' && authState.user) {
        clearTokenRefreshSchedule()
        authState.signout({
          showToast: false,
          showLoading: false,
        })
      }
    }

    // Add event listener for the custom session-expired event
    const subscription = DeviceEventEmitter.addListener(
      SESSION_EXPIRED_EVENT_NAME,
      handleSessionExpired,
    )

    // Clean up on unmount
    return () => {
      subscription.remove()
    }
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authState.status, authState.user])

  return (
    <AuthenticationContext.Provider value={authState}>{children}</AuthenticationContext.Provider>
  )
}
