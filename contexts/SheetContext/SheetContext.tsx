import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetFlatList,
  BottomSheetProps,
  BottomSheetScrollView,
  BottomSheetSectionList,
  BottomSheetView,
  BottomSheetVirtualizedList,
  SNAP_POINT_TYPE,
} from '@gorhom/bottom-sheet'
import React, { createContext, ReactNode, useCallback, useContext, useRef, useState } from 'react'
import {
  FlatListProps,
  ListRenderItem,
  ScrollViewProps,
  SectionListData,
  SectionListProps,
  VirtualizedListProps,
} from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'

const DEFAULT_SNAP_POINTS = ['25%', '50%', '90%']

// Types
type SheetType = 'basic' | 'scroll' | 'flatlist' | 'sectionlist' | 'virtualizedlist' | 'custom'

export interface SheetRef {
  close: () => void
  expand: () => void
  collapse: () => void
  snapToIndex: (index: number) => void
  snapToPosition: (position: string | number) => void
  forceClose: () => void
  logSheetState: () => void
  updateContent: (newChildren: ReactNode) => void
  updateProps: (newProps: Partial<SheetBaseProps>) => void
  updateFlatListData: (newData: unknown[]) => void
  appendFlatListData: (newData: unknown[]) => void
  updateSectionListData: (newSections: readonly SectionListData<unknown, unknown>[]) => void
  isExpanded: boolean
  isCollapsed: boolean
}

// Define custom types for the BottomSheet components
type BottomSheetScrollViewProps = ScrollViewProps
type BottomSheetFlatListProps<T> = FlatListProps<T>
type BottomSheetSectionListProps<T, S> = SectionListProps<T, S>
type BottomSheetVirtualizedListProps<T> = VirtualizedListProps<T>

interface SheetOptions extends Partial<SheetBaseProps> {
  type?: SheetType
  // Props specific to different sheet types
  flatListProps?: Partial<BottomSheetFlatListProps<unknown>>
  sectionListProps?: Partial<BottomSheetSectionListProps<unknown, unknown>>
  virtualizedListProps?: Partial<BottomSheetVirtualizedListProps<unknown>>
  scrollViewProps?: Partial<BottomSheetScrollViewProps>
  initialSnapIndex?: number
  renderSearchInput?: () => ReactNode
}

interface SheetBaseProps extends Omit<BottomSheetProps, 'children'> {}
interface SheetContextValue {
  openSheet: ({
    children,
    baseProps,
    options,
  }: {
    children: ReactNode
    baseProps?: SheetBaseProps
    options?: SheetOptions
  }) => SheetRef
  closeSheet: () => void
  isOpen: boolean
}

interface SheetProviderProps {
  children: ReactNode
  defaultProps?: Partial<SheetBaseProps>
}

interface SheetState {
  isOpen: boolean
  children: ReactNode | null
  options: SheetOptions
  initialSnapIndex?: number
}

// Context
const SheetContext = createContext<SheetContextValue | undefined>(undefined)

// Provider Component
export const SheetProvider: React.FC<SheetProviderProps> = ({ children, defaultProps = {} }) => {
  const bottomSheetRef = useRef<BottomSheet>(null)
  const [sheetState, setSheetState] = useState<SheetState>({
    isOpen: false,
    children: null,
    options: {},
    initialSnapIndex: 0,
  })

  const openSheet = ({
    children,
    baseProps,
    options,
  }: {
    children: ReactNode
    baseProps?: SheetBaseProps
    options?: SheetOptions
  }): SheetRef => {
    const mergedOptions = { ...defaultProps, ...baseProps, ...options }
    const indexAbleToSnap =
      (mergedOptions?.snapPoints as (string | number)[])?.length || DEFAULT_SNAP_POINTS.length
    setSheetState({
      isOpen: true,
      children,
      options: mergedOptions,
      initialSnapIndex:
        mergedOptions?.initialSnapIndex && mergedOptions.initialSnapIndex < indexAbleToSnap
          ? mergedOptions.initialSnapIndex
          : 0,
    })

    // Open the bottom sheet
    bottomSheetRef.current?.snapToIndex(
      sheetState?.initialSnapIndex && sheetState.initialSnapIndex < indexAbleToSnap
        ? sheetState.initialSnapIndex
        : 0,
    )

    // Return ref object with methods
    const sheetRef: SheetRef = {
      close: () => {
        bottomSheetRef.current?.close()
      },
      expand: () => {
        bottomSheetRef.current?.expand()
      },
      collapse: () => {
        bottomSheetRef.current?.collapse()
      },
      snapToIndex: (index: number) => {
        bottomSheetRef.current?.snapToIndex(index)
      },
      snapToPosition: (position: string | number) => {
        bottomSheetRef.current?.snapToPosition(position)
      },
      forceClose: () => {
        bottomSheetRef.current?.forceClose()
      },
      logSheetState: () => {
        console.log('Sheet State:', {
          isOpen: sheetState.isOpen,
          hasContent: !!sheetState.children,
          type: sheetState.options.type || 'basic',
          options: sheetState.options,
        })
      },
      updateContent: (newChildren: ReactNode) => {
        setSheetState((prev) => ({
          ...prev,
          children: newChildren,
        }))
      },
      updateProps: (newProps: Partial<SheetBaseProps>) => {
        setSheetState((prev) => ({
          ...prev,
          options: { ...prev.options, ...newProps },
        }))
      },
      // NEW: Add data update methods
      updateFlatListData: (newData: unknown[]) => {
        setSheetState((prev) => ({
          ...prev,
          options: {
            ...prev.options,
            flatListProps: {
              ...prev.options.flatListProps,
              data: newData,
            },
          },
        }))
      },
      appendFlatListData: (newData: unknown[]) => {
        setSheetState((prev) => {
          const currentData = prev.options.flatListProps?.data || []
          const updatedData = [...(currentData as unknown[]), ...newData]
          return {
            ...prev,
            options: {
              ...prev.options,
              flatListProps: {
                ...prev.options.flatListProps,
                data: updatedData,
              },
            },
          }
        })
      },
      updateSectionListData: (newSections: readonly SectionListData<unknown, unknown>[]) => {
        setSheetState((prev) => ({
          ...prev,
          options: {
            ...prev.options,
            sectionListProps: {
              ...prev.options.sectionListProps,
              sections: newSections,
            },
          },
        }))
      },
      isExpanded: sheetState.isOpen,
      isCollapsed: !sheetState.isOpen,
    }

    return sheetRef
  }

  const closeSheet = () => {
    bottomSheetRef.current?.close()
  }

  const handleSheetChange = (index: number, position: number, type: SNAP_POINT_TYPE) => {
    const isOpen = index >= 0
    setSheetState((prev) => ({
      ...prev,
      isOpen,
    }))

    // Clear content when sheet is closed
    if (!isOpen) {
      setSheetState((prev) => ({
        ...prev,
        children: null,
        options: {},
      }))
    }
    if (sheetState.options.onChange) {
      sheetState.options?.onChange?.(index, position, type)
    }
  }

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        onPress={closeSheet}
      />
    ),
    [],
  )

  const value: SheetContextValue = {
    openSheet,
    closeSheet,
    isOpen: sheetState.isOpen,
  }

  const renderSheetContent = () => {
    const { type, flatListProps, sectionListProps, virtualizedListProps, scrollViewProps } =
      sheetState.options

    switch (type) {
      case 'scroll':
        return (
          <BottomSheetScrollView {...scrollViewProps}>{sheetState.children}</BottomSheetScrollView>
        )

      case 'flatlist': {
        return (
          <BottomSheetFlatList
            {...flatListProps}
            renderItem={flatListProps?.renderItem}
            data={flatListProps?.data}
          />
        )
      }

      case 'sectionlist':
        return (
          <BottomSheetSectionList
            {...sectionListProps}
            renderItem={sectionListProps?.renderItem}
            sections={sectionListProps?.sections || []}
          />
        )

      case 'virtualizedlist':
        return (
          <BottomSheetVirtualizedList
            {...virtualizedListProps}
            renderItem={virtualizedListProps?.renderItem}
            getItem={virtualizedListProps?.getItem}
            getItemCount={virtualizedListProps?.getItemCount}
          />
        )

      case 'custom':
        return sheetState.children

      default:
        return <BottomSheetView>{sheetState.children}</BottomSheetView>
    }
  }

  const inset = useSafeAreaInsets()

  return (
    <SheetContext.Provider value={value}>
      {children}
      <BottomSheet
        topInset={inset.top}
        ref={bottomSheetRef}
        index={-1} // Start closed
        snapPoints={DEFAULT_SNAP_POINTS}
        enablePanDownToClose
        onChange={handleSheetChange}
        backdropComponent={renderBackdrop}
        {...sheetState.options}
      >
        {sheetState.options.renderSearchInput && sheetState.options.renderSearchInput()}
        {renderSheetContent()}
      </BottomSheet>
    </SheetContext.Provider>
  )
}

// Hook
export const useSheet = (): SheetContextValue => {
  const context = useContext(SheetContext)
  if (!context) {
    throw new Error('useSheet must be used within a SheetProvider')
  }
  return context
}

// Optional: Convenience hook for common sheet operations
export const useSheetActions = () => {
  const { openSheet, closeSheet, isOpen } = useSheet()

  const openBasicSheet = ({
    children,
    baseProps,
    options,
    renderSearchInput,
  }: {
    children: ReactNode
    baseProps?: SheetBaseProps
    options?: SheetOptions
    renderSearchInput?: () => ReactNode
  }): SheetRef => {
    return openSheet({
      children,
      baseProps,
      options: {
        type: 'basic',
        ...options,
        renderSearchInput,
      },
    })
  }

  const openScrollSheet = ({
    children,
    baseProps,
    options,
    renderSearchInput,
  }: {
    children: ReactNode
    baseProps?: SheetBaseProps
    options?: SheetOptions
    renderSearchInput?: () => ReactNode
  }): SheetRef => {
    return openSheet({
      children,
      baseProps,
      options: {
        type: 'scroll',
        ...options,
        renderSearchInput,
      },
    })
  }

  // Generic types for the list-based sheets
  const openFlatListSheet = <ItemT,>({
    data,
    renderItem,
    options,
    baseProps,
    renderSearchInput,
  }: {
    data: readonly ItemT[]
    renderItem: ListRenderItem<ItemT>
    options?: SheetOptions
    baseProps?: SheetBaseProps
    renderSearchInput?: () => ReactNode
  }): SheetRef => {
    return openSheet({
      children: null,
      baseProps,
      options: {
        type: 'flatlist',
        renderSearchInput,
        flatListProps: {
          ...options?.flatListProps,
          data,
          renderItem: renderItem as unknown as ListRenderItem<unknown>,
        },
      },
    })
  }

  const openSectionListSheet = <ItemT, SectionT>({
    sections,
    renderItem,
    options,
    baseProps,
    renderSearchInput,
  }: {
    sections: readonly SectionListData<ItemT, SectionT>[]
    renderItem: ListRenderItem<ItemT>
    options?: SheetOptions
    baseProps?: SheetBaseProps
    renderSearchInput?: () => ReactNode
  }): SheetRef => {
    return openSheet({
      children: null,
      baseProps,
      options: {
        type: 'sectionlist',
        renderSearchInput,
        sectionListProps: {
          sections: sections as unknown as readonly SectionListData<unknown, unknown>[],
          renderItem: renderItem as unknown as ListRenderItem<unknown>,
          ...options?.sectionListProps,
        },
      },
    })
  }

  const openVirtualizedListSheet = <ItemT,>({
    getItem,
    getItemCount,
    renderItem,
    data,
    options,
    baseProps,
    renderSearchInput,
  }: {
    getItem: (data: unknown, index: number) => ItemT
    getItemCount: (data: unknown) => number
    renderItem: ListRenderItem<ItemT>
    data: unknown
    options?: SheetOptions
    baseProps?: SheetBaseProps
    renderSearchInput?: () => ReactNode
  }): SheetRef => {
    return openSheet({
      children: null,
      baseProps,
      options: {
        type: 'virtualizedlist',
        renderSearchInput,
        virtualizedListProps: {
          data,
          renderItem: renderItem as unknown as ListRenderItem<unknown>,
          getItem: getItem as unknown as (data: unknown, index: number) => unknown,
          getItemCount,
          ...options?.virtualizedListProps,
        },
      },
    })
  }

  const openCustomSheet = ({
    children,
    baseProps,
    options,
    renderSearchInput,
  }: {
    children: ReactNode
    baseProps?: SheetBaseProps
    options?: SheetOptions
    renderSearchInput?: () => ReactNode
  }): SheetRef => {
    return openSheet({
      children,
      baseProps,
      options: {
        type: 'custom',
        ...options,
        renderSearchInput,
      },
    })
  }

  return {
    openSheet,
    closeSheet,
    isOpen,
    openBasicSheet,
    openScrollSheet,
    openFlatListSheet,
    openSectionListSheet,
    openVirtualizedListSheet,
    openCustomSheet,
  }
}
