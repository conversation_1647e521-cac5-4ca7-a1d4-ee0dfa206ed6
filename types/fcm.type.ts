import { FirebaseMessagingTypes } from '@react-native-firebase/messaging'

// FCM Authorization Status
export type FCMAuthorizationStatus = FirebaseMessagingTypes.AuthorizationStatus

// FCM Remote Message
export type FCMRemoteMessage = FirebaseMessagingTypes.RemoteMessage

// FCM Notification Data
export interface FCMNotificationData {
  type?: 'new_message' | 'appointment_reminder' | 'medicine_reminder' | 'general'
  chatId?: string
  appointmentId?: string
  medicineId?: string
  userId?: string
  title?: string
  body?: string
  imageUrl?: string
  deepLink?: string
  timestamp?: number
  priority?: 'high' | 'normal' | 'low'
  category?: string
  badge?: number
  [key: string]: any
}

// Parsed FCM Message
export interface FCMMessage {
  messageId: string
  data?: FCMNotificationData
  notification?: {
    title?: string
    body?: string
    imageUrl?: string
  }
  from?: string
  to?: string
  collapseKey?: string
  sentTime?: number
  ttl?: number
}

// FCM Topic Subscription
export interface FCMTopicSubscription {
  topic: string
  subscribed: boolean
  subscribedAt?: Date
}

// FCM Service Configuration
export interface FCMConfig {
  autoInitEnabled?: boolean
  enableAnalytics?: boolean
  foregroundPresentationOptions?: string[]
  backgroundHandlerTimeout?: number
  notificationChannelId?: string
  notificationColor?: string
}

// FCM Handler Types
export type FCMMessageListener = (remoteMessage: FCMRemoteMessage) => void
export type FCMNotificationOpenedListener = (remoteMessage: FCMRemoteMessage) => void
export type FCMTokenRefreshListener = (token: string) => void

// FCM Permission Request Result
export interface FCMPermissionResult {
  status: FCMAuthorizationStatus
  granted: boolean
}

// FCM Registration Result
export interface FCMRegistrationResult {
  success: boolean
  token?: string
  error?: string
}

// FCM Topic Management
export interface FCMTopicManager {
  subscribeToTopic: (topic: string) => Promise<void>
  unsubscribeFromTopic: (topic: string) => Promise<void>
  getSubscribedTopics: () => Promise<string[]>
}

// FCM Notification Categories (iOS)
export interface FCMNotificationCategory {
  id: string
  actions: FCMNotificationAction[]
  intentIdentifiers?: string[]
  options?: string[]
}

export interface FCMNotificationAction {
  id: string
  title: string
  options?: string[]
  textInputButtonTitle?: string
  textInputPlaceholder?: string
}

// FCM Analytics Event
export interface FCMAnalyticsEvent {
  name: string
  parameters?: Record<string, any>
  timestamp?: Date
}

// FCM Background Handler Context
export interface FCMBackgroundContext {
  isBackground: boolean
  isHeadless: boolean
  appState: 'active' | 'background' | 'inactive'
}

// FCM Error Types
export interface FCMError {
  code: string
  message: string
  details?: any
}

// FCM Service State
export interface FCMServiceState {
  isInitialized: boolean
  token: string | null
  isRegistered: boolean
  permissionStatus: FCMAuthorizationStatus | null
  subscribedTopics: string[]
  lastError: FCMError | null
}

// FCM Notification Display Options
export interface FCMDisplayOptions {
  showInForeground?: boolean
  playSound?: boolean
  vibrate?: boolean
  showBadge?: boolean
  priority?: 'high' | 'normal' | 'low'
  channelId?: string
}
