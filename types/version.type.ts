export interface Version {
  id: string
  name: string
  shortDescription: string
  content: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  }
  releasedAt?: string | null
  publishedAt?: string | null
  slug?: string | null
  slugLock?: boolean | null
  updatedAt: string
  createdAt: string
  _status?: ('draft' | 'published') | null
}
