# Learn more: https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Environment
.env
.env.prod
.env*.local

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native builds and credentials
.kotlin/
*.orig.*
*.jks
*.p8
*.p12
*.key
*.pem
*.mobileprovision

# Firebase config (ignore in Git, include in EAS build!)
android/app/google-services.json
ios/GoogleService-Info.plist
google-services.json
GoogleService-Info.plist

# Metro
.metro-health-check*

# Debug logs
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store

# TypeScript
*.tsbuildinfo

# Samples / examples
app-example
