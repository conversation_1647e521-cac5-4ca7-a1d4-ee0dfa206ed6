import {
  AuthorizationStatus,
  deleteToken,
  FirebaseMessagingTypes,
  getInitialNotification,
  getMessaging,
  getToken,
  hasPermission,
  isDeviceRegisteredForRemoteMessages,
  onMessage,
  onNotificationOpenedApp,
  onTokenRefresh,
  registerDeviceForRemoteMessages,
  requestPermission,
  setAutoInitEnabled,
  setBackgroundMessageHandler,
  subscribeToTopic,
  unregisterDeviceForRemoteMessages,
  unsubscribeFromTopic,
} from '@react-native-firebase/messaging'
import Constants from 'expo-constants'
import { Platform } from 'react-native'

export interface FCMNotificationData {
  type?: string
  chatId?: string
  appointmentId?: string
  medicineId?: string
  [key: string]: any
}

export interface FCMMessage {
  messageId: string
  data?: FCMNotificationData
  notification?: {
    title?: string
    body?: string
    imageUrl?: string
  }
  from?: string
  to?: string
  collapseKey?: string
  sentTime?: number
  ttl?: number
}

export type FCMAuthorizationStatus = FirebaseMessagingTypes.AuthorizationStatus
export type FCMNotificationOpenedListener = (
  remoteMessage: FirebaseMessagingTypes.RemoteMessage,
) => void
export type FCMMessageListener = (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => void

class FCMService {
  private static instance: FCMService
  private backgroundMessageHandler: FCMMessageListener | null = null
  private foregroundMessageHandler: FCMMessageListener | null = null
  private notificationOpenedHandler: FCMNotificationOpenedListener | null = null
  private unsubscribeForegroundListener: (() => void) | null = null
  private unsubscribeNotificationOpenedListener: (() => void) | null = null
  private isFirebaseAvailable: boolean = false

  private constructor() {
    // Check if Firebase is available
    this.checkFirebaseAvailability()

    // Initialize FCM only if available
    if (this.isFirebaseAvailable) {
      this.initializeFCM()
    }
  }

  public static getInstance(): FCMService {
    if (!FCMService.instance) {
      FCMService.instance = new FCMService()
    }
    return FCMService.instance
  }

  /**
   * Check if Firebase is available (not in Expo Go)
   */
  private checkFirebaseAvailability(): void {
    try {
      // Check if we're in Expo Go
      if (Constants.appOwnership === 'expo') {
        console.warn(
          'FCM Service: Firebase is not available in Expo Go. Please use a development build or EAS Build.',
        )
        this.isFirebaseAvailable = false
        return
      }

      // Try to access Firebase messaging
      const messagingInstance = getMessaging()
      if (messagingInstance) {
        this.isFirebaseAvailable = true
        console.log('FCM Service: Firebase messaging is available')
      }
    } catch (error) {
      console.warn('FCM Service: Firebase messaging is not available:', error)
      this.isFirebaseAvailable = false
    }
  }

  /**
   * Initialize FCM with default settings
   */
  private async initializeFCM(): Promise<void> {
    if (!this.isFirebaseAvailable) {
      console.warn('FCM Service: Skipping initialization - Firebase not available')
      return
    }

    try {
      const messagingInstance = getMessaging()

      // Check if FCM is supported
      if (!isDeviceRegisteredForRemoteMessages(messagingInstance)) {
        await registerDeviceForRemoteMessages(messagingInstance)
      }

      // Set auto initialization enabled
      await setAutoInitEnabled(messagingInstance, true)

      console.log('FCM initialized successfully')
    } catch (error) {
      console.error('Failed to initialize FCM:', error)
    }
  }

  /**
   * Request notification permissions (iOS)
   */
  async requestPermission(): Promise<FCMAuthorizationStatus> {
    if (!this.isFirebaseAvailable) {
      console.warn('FCM Service: requestPermission called but Firebase not available')
      return AuthorizationStatus.DENIED
    }

    try {
      const messagingInstance = getMessaging()
      const authStatus = await requestPermission(messagingInstance)

      const enabled =
        authStatus === AuthorizationStatus.AUTHORIZED ||
        authStatus === AuthorizationStatus.PROVISIONAL

      if (enabled) {
        console.log('FCM Authorization status:', authStatus)
      } else {
        console.log('FCM permission not granted:', authStatus)
      }

      return authStatus
    } catch (error) {
      console.error('Failed to request FCM permissions:', error)
      throw error
    }
  }

  /**
   * Check current permission status
   */
  async checkPermission(): Promise<FCMAuthorizationStatus> {
    if (!this.isFirebaseAvailable) {
      console.warn('FCM Service: checkPermission called but Firebase not available')
      return AuthorizationStatus.DENIED
    }

    try {
      const messagingInstance = getMessaging()
      return await hasPermission(messagingInstance)
    } catch (error) {
      console.error('Failed to check FCM permissions:', error)
      throw error
    }
  }

  /**
   * Get FCM token for this device
   */
  async getToken(): Promise<string> {
    if (!this.isFirebaseAvailable) {
      console.warn('FCM Service: getToken called but Firebase not available')
      throw new Error('Firebase messaging not available')
    }

    try {
      console.log('FCM Service: Getting messaging instance...')
      const messagingInstance = getMessaging()
      console.log('FCM Service: Messaging instance obtained, getting token...')

      const token = await getToken(messagingInstance)
      console.log(
        'FCM Service: Token retrieved successfully:',
        token ? `${token.substring(0, 20)}...` : 'null',
      )

      if (!token) {
        throw new Error('FCM token is null or empty')
      }

      return token
    } catch (error) {
      console.error('FCM Service: Failed to get FCM token:', error)
      console.error('FCM Service: Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        isFirebaseAvailable: this.isFirebaseAvailable,
      })
      throw error
    }
  }

  /**
   * Delete FCM token
   */
  async deleteToken(): Promise<void> {
    if (!this.isFirebaseAvailable) {
      console.warn('FCM Service: deleteToken called but Firebase not available')
      return
    }

    try {
      const messagingInstance = getMessaging()
      await deleteToken(messagingInstance)
      console.log('FCM token deleted')
    } catch (error) {
      console.error('Failed to delete FCM token:', error)
      throw error
    }
  }

  /**
   * Subscribe to a topic
   */
  async subscribeToTopic(topic: string): Promise<void> {
    if (!this.isFirebaseAvailable) {
      console.warn('FCM Service: subscribeToTopic called but Firebase not available')
      return
    }

    try {
      const messagingInstance = getMessaging()
      await subscribeToTopic(messagingInstance, topic)
      console.log(`Subscribed to FCM topic: ${topic}`)
    } catch (error) {
      console.error(`Failed to subscribe to topic ${topic}:`, error)
      throw error
    }
  }

  /**
   * Unsubscribe from a topic
   */
  async unsubscribeFromTopic(topic: string): Promise<void> {
    if (!this.isFirebaseAvailable) {
      console.warn('FCM Service: unsubscribeFromTopic called but Firebase not available')
      return
    }

    try {
      const messagingInstance = getMessaging()
      await unsubscribeFromTopic(messagingInstance, topic)
      console.log(`Unsubscribed from FCM topic: ${topic}`)
    } catch (error) {
      console.error(`Failed to unsubscribe from topic ${topic}:`, error)
      throw error
    }
  }

  /**
   * Set background message handler
   */
  setBackgroundMessageHandler(handler: FCMMessageListener): void {
    if (!this.isFirebaseAvailable) {
      console.warn('FCM Service: setBackgroundMessageHandler called but Firebase not available')
      return
    }

    this.backgroundMessageHandler = handler
    const messagingInstance = getMessaging()
    setBackgroundMessageHandler(
      messagingInstance,
      async (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
        console.log('Message handled in the background!', remoteMessage)
        if (this.backgroundMessageHandler) {
          this.backgroundMessageHandler(remoteMessage)
        }
      },
    )
  }

  /**
   * Set foreground message handler
   */
  setForegroundMessageHandler(handler: FCMMessageListener): void {
    if (!this.isFirebaseAvailable) {
      console.warn('FCM Service: setForegroundMessageHandler called but Firebase not available')
      return
    }

    this.foregroundMessageHandler = handler

    // Remove existing listener if any
    if (this.unsubscribeForegroundListener) {
      this.unsubscribeForegroundListener()
    }

    // Set new listener
    const messagingInstance = getMessaging()
    this.unsubscribeForegroundListener = onMessage(
      messagingInstance,
      async (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
        console.log('A new FCM message arrived in foreground!', remoteMessage)
        if (this.foregroundMessageHandler) {
          this.foregroundMessageHandler(remoteMessage)
        }
      },
    )
  }

  /**
   * Set notification opened handler
   */
  setNotificationOpenedHandler(handler: FCMNotificationOpenedListener): void {
    if (!this.isFirebaseAvailable) {
      console.warn('FCM Service: setNotificationOpenedHandler called but Firebase not available')
      return
    }

    this.notificationOpenedHandler = handler

    // Remove existing listener if any
    if (this.unsubscribeNotificationOpenedListener) {
      this.unsubscribeNotificationOpenedListener()
    }

    const messagingInstance = getMessaging()

    // Handle notification opened app from background state
    this.unsubscribeNotificationOpenedListener = onNotificationOpenedApp(
      messagingInstance,
      (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
        console.log('Notification caused app to open from background state:', remoteMessage)
        if (this.notificationOpenedHandler) {
          this.notificationOpenedHandler(remoteMessage)
        }
      },
    )

    // Handle notification opened app from quit state
    getInitialNotification(messagingInstance).then((remoteMessage) => {
      if (remoteMessage) {
        console.log('Notification caused app to open from quit state:', remoteMessage)
        if (this.notificationOpenedHandler) {
          this.notificationOpenedHandler(remoteMessage)
        }
      }
    })
  }

  /**
   * Setup complete FCM listeners
   */
  setupMessageHandlers(
    foregroundHandler?: FCMMessageListener,
    backgroundHandler?: FCMMessageListener,
    notificationOpenedHandler?: FCMNotificationOpenedListener,
  ): void {
    if (!this.isFirebaseAvailable) {
      console.warn('FCM Service: setupMessageHandlers called but Firebase not available')
      return
    }

    if (foregroundHandler) {
      this.setForegroundMessageHandler(foregroundHandler)
    }

    if (backgroundHandler) {
      this.setBackgroundMessageHandler(backgroundHandler)
    }

    if (notificationOpenedHandler) {
      this.setNotificationOpenedHandler(notificationOpenedHandler)
    }
  }

  /**
   * Get notification categories for iOS
   */
  async getNotificationCategories(): Promise<any[]> {
    try {
      if (Platform.OS === 'ios') {
        // You can define custom notification categories here
        return []
      }
      return []
    } catch (error) {
      console.error('Failed to get notification categories:', error)
      return []
    }
  }

  /**
   * Set notification categories for iOS
   */
  async setNotificationCategories(categories: any[]): Promise<void> {
    try {
      if (Platform.OS === 'ios') {
        // Set custom notification categories
        console.log('Setting notification categories:', categories)
      }
    } catch (error) {
      console.error('Failed to set notification categories:', error)
    }
  }

  /**
   * Handle token refresh
   */
  onTokenRefresh(callback: (token: string) => void): () => void {
    if (!this.isFirebaseAvailable) {
      console.warn('FCM Service: onTokenRefresh called but Firebase not available')
      return () => {}
    }

    const messagingInstance = getMessaging()
    return onTokenRefresh(messagingInstance, callback)
  }

  /**
   * Cleanup all listeners
   */
  cleanup(): void {
    if (this.unsubscribeForegroundListener) {
      this.unsubscribeForegroundListener()
      this.unsubscribeForegroundListener = null
    }

    if (this.unsubscribeNotificationOpenedListener) {
      this.unsubscribeNotificationOpenedListener()
      this.unsubscribeNotificationOpenedListener = null
    }

    this.backgroundMessageHandler = null
    this.foregroundMessageHandler = null
    this.notificationOpenedHandler = null
  }

  /**
   * Check if device is registered for remote messages
   */
  async isRegisteredForRemoteMessages(): Promise<boolean> {
    if (!this.isFirebaseAvailable) {
      return false
    }

    try {
      const messagingInstance = getMessaging()
      return isDeviceRegisteredForRemoteMessages(messagingInstance)
    } catch (error) {
      console.error('Failed to check remote message registration:', error)
      return false
    }
  }

  /**
   * Register device for remote messages
   */
  async registerForRemoteMessages(): Promise<void> {
    if (!this.isFirebaseAvailable) {
      console.warn('FCM Service: registerForRemoteMessages called but Firebase not available')
      return
    }

    try {
      const messagingInstance = getMessaging()
      if (!isDeviceRegisteredForRemoteMessages(messagingInstance)) {
        await registerDeviceForRemoteMessages(messagingInstance)
        console.log('Device registered for remote messages')
      }
    } catch (error) {
      console.error('Failed to register for remote messages:', error)
      throw error
    }
  }

  /**
   * Unregister device from remote messages
   */
  async unregisterForRemoteMessages(): Promise<void> {
    if (!this.isFirebaseAvailable) {
      console.warn('FCM Service: unregisterForRemoteMessages called but Firebase not available')
      return
    }

    try {
      const messagingInstance = getMessaging()
      if (isDeviceRegisteredForRemoteMessages(messagingInstance)) {
        await unregisterDeviceForRemoteMessages(messagingInstance)
        console.log('Device unregistered from remote messages')
      }
    } catch (error) {
      console.error('Failed to unregister from remote messages:', error)
      throw error
    }
  }

  /**
   * Parse FCM message data
   */
  parseMessageData(remoteMessage: FirebaseMessagingTypes.RemoteMessage): FCMMessage {
    return {
      messageId: remoteMessage.messageId || '',
      data: remoteMessage.data as FCMNotificationData,
      notification: remoteMessage.notification
        ? {
            title: remoteMessage.notification.title,
            body: remoteMessage.notification.body,
            imageUrl: remoteMessage.notification.android?.imageUrl,
          }
        : undefined,
      from: remoteMessage.from,
      to: remoteMessage.to,
      collapseKey: remoteMessage.collapseKey,
      sentTime: remoteMessage.sentTime,
      ttl: remoteMessage.ttl,
    }
  }

  /**
   * Check if Firebase is available
   */
  get isAvailable(): boolean {
    return this.isFirebaseAvailable
  }
}

export const fcmService = FCMService.getInstance()
export { FCMService }
