import { APP_CHANNEL, ChannelConfig } from '@/constants/app-notification.constant'
import * as Notifications from 'expo-notifications'
import { Platform } from 'react-native'

export interface NotificationOptions {
  title?: string
  body?: string
  data?: any
  sound?: boolean
  badge?: number
  trigger?: Notifications.NotificationTriggerInput | null
  channelId?: string
}

export interface ScheduledNotification {
  identifier: string
  content: Notifications.NotificationContent
  trigger: Notifications.NotificationTriggerInput | null
  date: Date
}

// Use the actual type from expo-notifications
export type NotificationPermissionsStatus = Notifications.NotificationPermissionsStatus

class AppNotificationService {
  private static instance: AppNotificationService

  private constructor() {
    // Initialize notification handler
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldPlaySound: true,
        shouldSetBadge: true,
        shouldShowBanner: true,
        shouldShowList: true,
      }),
    })
  }

  public static getInstance(): AppNotificationService {
    if (!AppNotificationService.instance) {
      AppNotificationService.instance = new AppNotificationService()
    }
    return AppNotificationService.instance
  }

  /**
   * Send a local notification with channel-specific settings
   */
  async sendLocalNotification(options: NotificationOptions): Promise<string> {
    const {
      title = 'Notification',
      body = 'This is a notification',
      data = {},
      sound = true,
      badge = 1,
      trigger = null,
      channelId = APP_CHANNEL.DEFAULT.id,
    } = options

    try {
      // Ensure the notification channel exists (for Android)
      if (Platform.OS === 'android') {
        await this.ensureNotificationChannel(channelId)
      }

      // Get channel config to apply channel-specific settings
      const channelConfig = this.getChannelConfig(channelId)

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: channelConfig.sound && sound, // Use channel setting if available
          ...(channelConfig.showBadge ? { badge } : {}), // Only include badge if showBadge is true
        },
        trigger,
      })

      console.log(
        `Local notification scheduled with ID: ${notificationId} on channel: ${channelId}`,
      )
      return notificationId
    } catch (error) {
      console.error('Failed to schedule local notification:', error)
      throw error
    }
  }

  /**
   * Send an immediate notification
   */
  async sendImmediateNotification(title: string, body: string, data?: any): Promise<string> {
    return this.sendLocalNotification({
      title,
      body,
      data,
      trigger: null, // null trigger means immediate notification
    })
  }

  /**
   * Send a delayed notification
   */
  async sendDelayedNotification(
    title: string,
    body: string,
    trigger: Notifications.NotificationTriggerInput,
    data?: any,
  ): Promise<string> {
    let processedTrigger: Notifications.NotificationTriggerInput

    // Handle null trigger
    if (!trigger) {
      processedTrigger = {
        type: Notifications.SchedulableTriggerInputTypes.TIME_INTERVAL,
        seconds: 0,
        repeats: false,
      }
    } else {
      // Check if it's a schedulable trigger
      if ('type' in trigger) {
        switch (trigger.type) {
          case Notifications.SchedulableTriggerInputTypes.TIME_INTERVAL:
            const timeTrigger = trigger as Notifications.TimeIntervalTriggerInput
            processedTrigger = {
              type: Notifications.SchedulableTriggerInputTypes.TIME_INTERVAL,
              seconds: timeTrigger.seconds || 0,
              repeats: timeTrigger.repeats || false,
              channelId: trigger.channelId || APP_CHANNEL.DEFAULT.id,
            }
            break

          case Notifications.SchedulableTriggerInputTypes.DATE:
            const dateTrigger = trigger as Notifications.DateTriggerInput
            processedTrigger = {
              type: Notifications.SchedulableTriggerInputTypes.DATE,
              date: dateTrigger.date,
              channelId: trigger.channelId || APP_CHANNEL.DEFAULT.id,
            }
            break

          case Notifications.SchedulableTriggerInputTypes.CALENDAR:
            const calendarTrigger = trigger as Notifications.CalendarTriggerInput
            const { type, ...rest } = calendarTrigger
            processedTrigger = {
              type: Notifications.SchedulableTriggerInputTypes.CALENDAR,
              channelId: trigger.channelId || APP_CHANNEL.DEFAULT.id,
              ...rest,
            }
            break

          default:
            // Default to immediate notification if trigger type is not recognized
            processedTrigger = {
              type: Notifications.SchedulableTriggerInputTypes.TIME_INTERVAL,
              seconds: 0,
              repeats: false,
              channelId: trigger.channelId || APP_CHANNEL.DEFAULT.id,
            }
            break
        }
      } else {
        // Handle channel-aware triggers or other types
        processedTrigger = {
          type: Notifications.SchedulableTriggerInputTypes.TIME_INTERVAL,
          seconds: 0,
          repeats: false,
          channelId: trigger.channelId || APP_CHANNEL.DEFAULT.id,
        }
      }
    }

    return this.sendLocalNotification({
      title,
      body,
      data,
      trigger: processedTrigger,
    })
  }

  /**
   * Cancel all scheduled notifications
   */
  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync()
      console.log('All scheduled notifications cancelled')
    } catch (error) {
      console.error('Failed to cancel notifications:', error)
      throw error
    }
  }

  /**
   * Cancel a specific notification by ID
   */
  async cancelNotification(notificationId: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId)
      console.log(`Notification ${notificationId} cancelled`)
    } catch (error) {
      console.error(`Failed to cancel notification ${notificationId}:`, error)
      throw error
    }
  }

  /**
   * Get all scheduled notifications
   */
  async getScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
    try {
      const notifications = await Notifications.getAllScheduledNotificationsAsync()
      console.log('Scheduled notifications:', notifications)
      return notifications
    } catch (error) {
      console.error('Failed to get scheduled notifications:', error)
      throw error
    }
  }

  /**
   * Get notification permissions status
   */
  async getPermissionsStatus(): Promise<Notifications.NotificationPermissionsStatus> {
    try {
      return await Notifications.getPermissionsAsync()
    } catch (error) {
      console.error('Failed to get permissions status:', error)
      throw error
    }
  }

  /**
   * Request notification permissions
   */
  async requestPermissions(): Promise<Notifications.NotificationPermissionsStatus> {
    try {
      return await Notifications.requestPermissionsAsync()
    } catch (error) {
      console.error('Failed to request permissions:', error)
      throw error
    }
  }

  /**
   * Set up notification channel for Android with channel-specific settings
   */
  async setupNotificationChannel(
    channelId: string = APP_CHANNEL.DEFAULT.id,
    channelName?: string,
  ): Promise<void> {
    try {
      const channelConfig = this.getChannelConfig(channelId)
      const name = channelName || channelConfig.name

      await Notifications.setNotificationChannelAsync(channelId, {
        name,
        description: channelConfig.description,
        importance: channelConfig.importance,
        vibrationPattern: channelConfig.vibrationPattern,
        lightColor: channelConfig.lightColor,
        enableLights: channelConfig.enableLights,
        enableVibrate: channelConfig.enableVibration,
        showBadge: channelConfig.showBadge,
      })
      console.log(`Notification channel ${channelId} set up successfully with custom settings`)
    } catch (error) {
      console.error('Failed to set up notification channel:', error)
      throw error
    }
  }

  /**
   * Ensure notification channel exists, create if it doesn't
   */
  private async ensureNotificationChannel(channelId: string): Promise<void> {
    try {
      // Check if channel already exists
      const channels = await Notifications.getNotificationChannelsAsync()
      const channelExists = channels.some((channel) => channel.id === channelId)

      if (!channelExists) {
        // Find channel config from constants
        const channelConfig = this.getChannelConfig(channelId)
        await this.setupNotificationChannel(channelId, channelConfig.name)
      }
    } catch (error) {
      console.error(`Failed to ensure notification channel ${channelId}:`, error)
      // Fallback to default channel
      await this.setupNotificationChannel(APP_CHANNEL.DEFAULT.id, APP_CHANNEL.DEFAULT.name)
    }
  }

  /**
   * Get channel configuration by ID
   */
  private getChannelConfig(channelId: string): ChannelConfig {
    // Find channel in APP_CHANNEL constants
    const channelKeys = Object.keys(APP_CHANNEL) as Array<keyof typeof APP_CHANNEL>
    for (const key of channelKeys) {
      if (APP_CHANNEL[key].id === channelId) {
        return APP_CHANNEL[key]
      }
    }

    // Return default if not found
    return APP_CHANNEL.DEFAULT
  }

  /**
   * Add notification received listener
   */
  addNotificationReceivedListener(
    listener: (notification: Notifications.Notification) => void,
  ): Notifications.EventSubscription {
    return Notifications.addNotificationReceivedListener(listener)
  }

  /**
   * Add notification response received listener
   */
  addNotificationResponseReceivedListener(
    listener: (response: Notifications.NotificationResponse) => void,
  ): Notifications.EventSubscription {
    return Notifications.addNotificationResponseReceivedListener(listener)
  }

  /**
   * Remove notification listener
   */
  removeNotificationListener(subscription: Notifications.Subscription): void {
    subscription.remove()
  }
}

export const appNotificationService = AppNotificationService.getInstance()
export { AppNotificationService }
