import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { PaginatedDocs } from '@/types/global.type'

import { Params } from '@/types/http.type'
import {
  CheckKeywordExistResponse,
  CreateMultipleKeywordsPayload,
  CreateMultipleKeywordsResponse,
  GetKeywordAudioPayload,
  GetKeywordAudioResponse,
  Keyword,
} from '@/types/keyword.type'

import { AxiosRequestConfig } from 'axios'
import { httpService } from '../http/http.service'

// SERVER / CLIENT
class KeywordService {
  private static instance: KeywordService

  private constructor() {}

  public static getInstance(): KeywordService {
    if (!KeywordService.instance) {
      KeywordService.instance = new KeywordService()
    }
    return KeywordService.instance
  }

  /**
   * Fetch keywords using the HTTP service with the fetch API.
   *
   * @param params - Optional query parameters for filtering or paginating keywords.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<Keyword> or null in case of an error.
   */
  public async getKeywords({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<Keyword> | null> {
    const data = await httpService.getWithMethodOverride<PaginatedDocs<Keyword>>(
      `/${API_ENDPOINTS.keywords_api}/list`,
      params,
      options,
    )
    return data
  }

  /**
   * Fetch keywords using the HTTP service with the fetch API.
   * This using full text search and only return id and name
   * @param params - Optional query parameters for filtering or paginating keywords.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<Keyword> or null in case of an error.
   */
  public async getKeywordsV2({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<Pick<Keyword, 'id' | 'name'>>> {
    const data = await httpService.getWithMethodOverride<
      PaginatedDocs<Pick<Keyword, 'id' | 'name'>>
    >(`/${API_ENDPOINTS.keywords_api}/full-text-search`, params, options)
    return data
  }

  /**
   * Create Multiple Keywords using the HTTP service.
   *
   * @param payload - The payload containing an array of keywords to create.
   * @param options - Optional axios request configuration.
   * @returns A promise resolving to CreateMultipleKeywordsResponse.
   */
  public async createMultipleKeywords(
    payload: CreateMultipleKeywordsPayload,
    options?: AxiosRequestConfig,
  ) {
    const formData = new FormData()
    formData.append('_payload', JSON.stringify(payload))

    const data = await httpService.post<CreateMultipleKeywordsResponse>(
      `/${API_ENDPOINTS.keywords_api}/create-multiple`,
      formData,
      options,
    )
    return data
  }

  public async getKeywordAudio({
    payload,

    options = {},
  }: {
    payload: GetKeywordAudioPayload

    options?: AxiosRequestConfig
  }): Promise<GetKeywordAudioResponse> {
    const formData = new FormData()
    formData.append('_payload', JSON.stringify(payload))
    const data = await httpService.post<GetKeywordAudioResponse>(
      `/${API_ENDPOINTS.keywords_api}/audio`,
      formData,
      options,
    )
    return data
  }

  /**
   * Fetch random keywords using the HTTP service with the fetch API.
   *
   * @param params - Optional query parameters for filtering or paginating keywords.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<Keyword> or null in case of an error.
   */
  public async getRandomKeywords({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<Keyword> | null> {
    const data = await httpService.get<PaginatedDocs<Keyword>>(
      `/${API_ENDPOINTS.keywords_api}/random`,
      { params, ...options },
    )
    return data
  }

  /**
   * Check if a keyword exists using the HTTP service with the fetch API.
   *
   * @param params - Optional query parameters for keyword:string.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to CheckKeywordExistResponse.
   */

  public async checkKeywordExist({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<CheckKeywordExistResponse> {
    const data = await httpService.get<CheckKeywordExistResponse>(
      `/${API_ENDPOINTS.keywords_api}/check-exist`,
      { params, ...options },
    )
    return data
  }

  /**
   * Fetch populated search keywords using the HTTP service with the fetch API.
   *
   * @param params - Optional query parameters for filtering or paginating keywords.
   * @param options - Optional fetch options such as headers, method, etc.
   * @returns A promise resolving to PaginatedDocs<Keyword> or null in case of an error.
   */
  public async getPopulatedSearchKeywords({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<Keyword> | null> {
    const data = await httpService.get<PaginatedDocs<Keyword>>(
      `/${API_ENDPOINTS.keywords_api}/populated-search`,
      { params, ...options },
    )
    return data
  }
}
export const keywordService = KeywordService.getInstance()
