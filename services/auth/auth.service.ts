import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { ACCESS_TOKEN, MAX_AGE_TOKEN, X_LOGIN_SESSION } from '@/constants/storage-key.constant'
import {
  GoogleLoginResponse,
  LoginResponse,
  RefreshTokenResponse,
  RegisterRequest,
  RegisterResponse,
  ResendVerifyEmailRequest,
  ResendVerifyEmailResponse,
  SendResetPasswordEmailRequest,
  SendResetPasswordEmailResponse,
} from '@/types/auth.type'
import { User } from '@/types/user.type'
import { AxiosRequestConfig } from 'axios'
import { httpService } from '../http/http.service'
import { secureStoreService } from '../secure-store/secure-store.service'

class AuthService {
  private static instance: AuthService

  private constructor() {}

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService()
    }
    return AuthService.instance
  }

  async signup(signupRequest: RegisterRequest): Promise<RegisterResponse> {
    const response: RegisterResponse = await httpService.post<RegisterResponse>(
      `/${API_ENDPOINTS.users_api}`,
      signupRequest,
    )
    return response
  }

  async login(email: string, password: string, lang?: string): Promise<LoginResponse> {
    const response: LoginResponse = await httpService.post(
      `/${API_ENDPOINTS.users_api}/login-mobile`,
      {
        email,
        password,
        lang,
      },
    )

    await secureStoreService.setItem(ACCESS_TOKEN, response.token ?? '')
    await secureStoreService.setItem(X_LOGIN_SESSION, response.sessionToken)
    await secureStoreService.setItem(MAX_AGE_TOKEN, response.exp.toString())

    return response
  }

  async getCurrentUser(): Promise<{ user: User; exp: number } | null> {
    const response = await httpService.get<{ user: User; exp: number }>(
      `/${API_ENDPOINTS.users_api}/me`,
    )
    return response
  }

  async refreshToken({
    options = {},
  }: {
    options?: AxiosRequestConfig
  } = {}): Promise<RefreshTokenResponse> {
    const response = await httpService.post<RefreshTokenResponse>(
      `/${API_ENDPOINTS.users_api}/refresh-token`,
      undefined,
      options,
    )
    return response
  }

  async logout({
    options = {},
  }: {
    options?: AxiosRequestConfig
  } = {}) {
    await httpService.post(`/${API_ENDPOINTS.users_api}/logout`, undefined, options)
    await secureStoreService.removeItem(ACCESS_TOKEN)
    await secureStoreService.removeItem(X_LOGIN_SESSION)
  }

  async resendVerifyEmail({
    email,
    language,
  }: ResendVerifyEmailRequest): Promise<ResendVerifyEmailResponse> {
    const response: ResendVerifyEmailResponse = await httpService.post(
      `/${API_ENDPOINTS.users_api}/resend-verify-email`,
      { email, language },
    )
    return response
  }

  async sendResetPasswordEmail({
    email,
    language,
  }: SendResetPasswordEmailRequest): Promise<SendResetPasswordEmailResponse> {
    const response: SendResetPasswordEmailResponse = await httpService.post(
      `/${API_ENDPOINTS.users_api}/send-reset-password-email`,
      { email, language },
    )
    return response
  }
  async googleLogin(idToken: string): Promise<GoogleLoginResponse> {
    const response: GoogleLoginResponse = await httpService.post<GoogleLoginResponse>(
      `/${API_ENDPOINTS.users_api}/google-login-mobile`,
      { idToken },
    )
    if (response) {
      await secureStoreService.setItem(ACCESS_TOKEN, response.token ?? '')
      await secureStoreService.setItem(X_LOGIN_SESSION, response.sessionToken)
      await secureStoreService.setItem(MAX_AGE_TOKEN, response.exp.toString())
    }

    return response
  }
}

export const authService = AuthService.getInstance()
export { AuthService }
