import { useNotification } from '@/hooks/useNotification'
import * as Notifications from 'expo-notifications'
import React from 'react'
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'

export const NotificationTester: React.FC = () => {
  const {
    sendImmediateNotification,
    sendDelayedNotification,
    sendLocalNotification,
    cancelAllNotifications,
    getScheduledNotifications,
  } = useNotification()

  const testImmediateNotification = async () => {
    try {
      await sendImmediateNotification('Immediate Test', 'This notification appears immediately!', {
        type: 'test',
        timestamp: Date.now(),
      })
      console.log('Immediate notification sent')
    } catch (error) {
      console.error('Failed to send immediate notification:', error)
    }
  }

  const testDelayedNotification = async () => {
    try {
      await sendDelayedNotification(
        'Delayed Test',
        'This notification appears after 5 seconds!',
        {
          type: Notifications.SchedulableTriggerInputTypes.TIME_INTERVAL,
          seconds: 5,
          repeats: false,
        },
        { type: 'delayed_test', timestamp: Date.now() },
      )
      console.log('Delayed notification scheduled')
    } catch (error) {
      console.error('Failed to send delayed notification:', error)
    }
  }

  const testCustomNotification = async () => {
    try {
      await sendLocalNotification({
        title: 'Custom Notification',
        body: 'This is a custom notification with specific data!',
        data: {
          type: 'custom',
          userId: '12345',
          action: 'open_profile',
          timestamp: Date.now(),
        },
        badge: 2,
        sound: true,
        channelId: 'alerts',
      })
      console.log('Custom notification sent')
    } catch (error) {
      console.error('Failed to send custom notification:', error)
    }
  }

  const testMedicineReminder = async () => {
    try {
      await sendLocalNotification({
        title: 'Medicine Reminder',
        body: 'Time to take your medicine!',
        data: {
          type: 'medicine_reminder',
          medicineId: 'med123',
          timestamp: Date.now(),
        },
        channelId: 'medicine',
        sound: true,
      })
      console.log('Medicine reminder sent')
    } catch (error) {
      console.error('Failed to send medicine reminder:', error)
    }
  }

  const testSilentUpdate = async () => {
    try {
      await sendLocalNotification({
        title: 'App Update Available',
        body: 'New version 2.1.0 is ready to download',
        data: {
          type: 'app_update',
          version: '2.1.0',
          timestamp: Date.now(),
        },
        channelId: 'updates',
        sound: false, // This will be overridden by channel settings
      })
      console.log('Silent update notification sent')
    } catch (error) {
      console.error('Failed to send update notification:', error)
    }
  }

  const testHighPriorityAlert = async () => {
    try {
      await sendLocalNotification({
        title: '🚨 Emergency Alert',
        body: 'Important system maintenance in 5 minutes',
        data: {
          type: 'emergency_alert',
          priority: 'high',
          timestamp: Date.now(),
        },
        channelId: 'alerts',
        sound: true,
        badge: 1,
      })
      console.log('High priority alert sent')
    } catch (error) {
      console.error('Failed to send alert:', error)
    }
  }

  const testReminder = async () => {
    try {
      await sendLocalNotification({
        title: 'Daily Reminder',
        body: "Don't forget to check your health metrics!",
        data: {
          type: 'daily_reminder',
          category: 'health',
          timestamp: Date.now(),
        },
        channelId: 'reminders',
        sound: true,
      })
      console.log('Daily reminder sent')
    } catch (error) {
      console.error('Failed to send reminder:', error)
    }
  }

  const handleCancelAll = async () => {
    try {
      await cancelAllNotifications()
      console.log('All notifications cancelled')
    } catch (error) {
      console.error('Failed to cancel notifications:', error)
    }
  }

  const handleGetScheduled = async () => {
    try {
      const notifications = await getScheduledNotifications()
      console.log('Current scheduled notifications:', notifications)
    } catch (error) {
      console.error('Failed to get scheduled notifications:', error)
    }
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Local Notification Tester</Text>

      <TouchableOpacity style={styles.button} onPress={testImmediateNotification}>
        <Text style={styles.buttonText}>Send Immediate Notification</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testDelayedNotification}>
        <Text style={styles.buttonText}>Send Delayed Notification (5s)</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testCustomNotification}>
        <Text style={styles.buttonText}>Send Custom Notification (Alerts)</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, styles.medicineButton]}
        onPress={testMedicineReminder}
      >
        <Text style={styles.buttonText}>Send Medicine Reminder</Text>
      </TouchableOpacity>

      <TouchableOpacity style={[styles.button, styles.updateButton]} onPress={testSilentUpdate}>
        <Text style={styles.buttonText}>Send Silent Update Notification</Text>
      </TouchableOpacity>

      <TouchableOpacity style={[styles.button, styles.alertButton]} onPress={testHighPriorityAlert}>
        <Text style={styles.buttonText}>Send High Priority Alert</Text>
      </TouchableOpacity>

      <TouchableOpacity style={[styles.button, styles.reminderButton]} onPress={testReminder}>
        <Text style={styles.buttonText}>Send Daily Reminder</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={handleGetScheduled}>
        <Text style={styles.buttonText}>Get Scheduled Notifications</Text>
      </TouchableOpacity>

      <TouchableOpacity style={[styles.button, styles.cancelButton]} onPress={handleCancelAll}>
        <Text style={styles.buttonText}>Cancel All Notifications</Text>
      </TouchableOpacity>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  cancelButton: {
    backgroundColor: '#FF3B30',
  },
  alertButton: {
    backgroundColor: '#F44336',
  },
  medicineButton: {
    backgroundColor: '#FF9800',
  },
  updateButton: {
    backgroundColor: '#2196F3',
  },
  reminderButton: {
    backgroundColor: '#4CAF50',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: '600',
  },
})
