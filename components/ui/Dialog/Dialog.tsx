'use client'
import { createModal } from '@gluestack-ui/modal'
import type { VariantProps } from '@gluestack-ui/nativewind-utils'
import { tva } from '@gluestack-ui/nativewind-utils/tva'
import { useStyleContext, withStyleContext } from '@gluestack-ui/nativewind-utils/withStyleContext'
import { cssInterop } from 'nativewind'
import React from 'react'
import { Pressable, ScrollView, View } from 'react-native'
import Animated, { FadeIn, FadeOut, Keyframe, ZoomIn } from 'react-native-reanimated'

const SCOPE = 'MODAL'

// Create Dialog-specific animated components using React Native Reanimated
// These are scoped only to Dialog and won't affect other components
const DialogAnimatedPressable = Animated.createAnimatedComponent(Pressable)
const DialogAnimatedView = Animated.createAnimatedComponent(View)

// Apply cssInterop only to these Dialog-specific components
cssInterop(DialogAnimatedPressable, { className: 'style' })
cssInterop(DialogAnimatedView, { className: 'style' })

DialogAnimatedPressable.displayName = 'DialogAnimatedPressable'
DialogAnimatedView.displayName = 'DialogAnimatedView'
// Custom exit animation using Keyframe to scale to 50%
const ZoomOutTo50 = new Keyframe({
  0: {
    transform: [{ scale: 1 }],
    opacity: 1,
  },
  100: {
    transform: [{ scale: 0.5 }],
    opacity: 0,
  },
}).duration(200)

// Simple wrapper to handle refs that gluestack-ui passes to AnimatePresence
const AnimatePresenceWrapper = React.forwardRef<any, any>(function AnimatePresenceWrapper(
  { children, ...props },
  ref,
) {
  return <>{children}</>
})

const UIModal = createModal({
  Root: withStyleContext(View, SCOPE),
  Backdrop: DialogAnimatedPressable,
  Content: DialogAnimatedView,
  Body: ScrollView,
  CloseButton: Pressable,
  Footer: View,
  Header: View,
  AnimatePresence: AnimatePresenceWrapper,
})

const modalStyle = tva({
  base: 'group/modal w-full h-full justify-center items-center ',
  variants: {
    size: {
      xs: '',
      sm: '',
      md: '',
      lg: '',
      full: '',
    },
  },
})

const modalBackdropStyle = tva({
  base: 'absolute left-0 top-0 right-0 bottom-0 bg-black/80',
})

const modalContentStyle = tva({
  base: 'bg-background-0 rounded-xl overflow-hidden border border-outline-100 shadow-hard-2 p-6',
  parentVariants: {
    size: {
      xs: 'w-[60%] max-w-[360px]',
      sm: 'w-[70%] max-w-[420px]',
      md: 'w-[80%] max-w-[510px]',
      lg: 'w-[90%] max-w-[640px]',
      full: 'w-full',
    },
  },
})

const modalBodyStyle = tva({
  base: 'mt-2 mb-6',
})

const modalCloseButtonStyle = tva({
  base: 'group/modal-close-button z-10 rounded cursor-pointer',
})

const modalHeaderStyle = tva({
  base: 'justify-between items-center flex-row',
})

const modalFooterStyle = tva({
  base: 'flex-row justify-end items-center gap-2',
})

type IModalProps = React.ComponentProps<typeof UIModal> &
  VariantProps<typeof modalStyle> & { className?: string }

type IModalBackdropProps = React.ComponentProps<typeof UIModal.Backdrop> &
  VariantProps<typeof modalBackdropStyle> & { className?: string }

type IModalContentProps = React.ComponentProps<typeof UIModal.Content> &
  VariantProps<typeof modalContentStyle> & { className?: string }

type IModalHeaderProps = React.ComponentProps<typeof UIModal.Header> &
  VariantProps<typeof modalHeaderStyle> & { className?: string }

type IModalBodyProps = React.ComponentProps<typeof UIModal.Body> &
  VariantProps<typeof modalBodyStyle> & { className?: string }

type IModalFooterProps = React.ComponentProps<typeof UIModal.Footer> &
  VariantProps<typeof modalFooterStyle> & { className?: string }

type IModalCloseButtonProps = React.ComponentProps<typeof UIModal.CloseButton> &
  VariantProps<typeof modalCloseButtonStyle> & { className?: string }

const Dialog = React.forwardRef<React.ComponentRef<typeof UIModal>, IModalProps>(
  ({ className, size = 'md', ...props }, ref) => (
    <UIModal
      ref={ref}
      {...props}
      pointerEvents="box-none"
      className={modalStyle({ size, class: className })}
      context={{ size }}
    />
  ),
)

const DialogBackdrop = React.forwardRef<
  React.ComponentRef<typeof UIModal.Backdrop>,
  IModalBackdropProps
>(function DialogBackdrop({ className, ...props }, ref) {
  return (
    <UIModal.Backdrop
      ref={ref}
      entering={FadeIn.duration(250)}
      exiting={FadeOut.duration(200)}
      {...props}
      className={modalBackdropStyle({
        class: className,
      })}
    />
  )
})

const DialogContent = React.forwardRef<
  React.ComponentRef<typeof UIModal.Content>,
  IModalContentProps
>(function DialogContent({ className, size, ...props }, ref) {
  const { size: parentSize } = useStyleContext(SCOPE)

  return (
    <UIModal.Content
      ref={ref}
      entering={ZoomIn.springify()
        .damping(15)
        .stiffness(300)
        .withInitialValues({ transform: [{ scale: 0.5 }] })}
      exiting={ZoomOutTo50}
      {...props}
      className={modalContentStyle({
        parentVariants: {
          size: parentSize,
        },
        size,
        class: className,
      })}
      pointerEvents="auto"
    />
  )
})

const DialogHeader = React.forwardRef<React.ComponentRef<typeof UIModal.Header>, IModalHeaderProps>(
  function DialogHeader({ className, ...props }, ref) {
    return (
      <UIModal.Header
        ref={ref}
        {...props}
        className={modalHeaderStyle({
          class: className,
        })}
      />
    )
  },
)

const DialogBody = React.forwardRef<React.ComponentRef<typeof UIModal.Body>, IModalBodyProps>(
  function DialogBody({ className, ...props }, ref) {
    return (
      <UIModal.Body
        ref={ref}
        {...props}
        className={modalBodyStyle({
          class: className,
        })}
      />
    )
  },
)

const DialogFooter = React.forwardRef<React.ComponentRef<typeof UIModal.Footer>, IModalFooterProps>(
  function DialogFooter({ className, ...props }, ref) {
    return (
      <UIModal.Footer
        ref={ref}
        {...props}
        className={modalFooterStyle({
          class: className,
        })}
      />
    )
  },
)

const DialogCloseButton = React.forwardRef<
  React.ComponentRef<typeof UIModal.CloseButton>,
  IModalCloseButtonProps
>(function DialogCloseButton({ className, ...props }, ref) {
  return (
    <UIModal.CloseButton
      ref={ref}
      {...props}
      className={modalCloseButtonStyle({
        class: className,
      })}
    />
  )
})

Dialog.displayName = 'Dialog'
DialogBackdrop.displayName = 'DialogBackdrop'
DialogContent.displayName = 'DialogContent'
DialogHeader.displayName = 'DialogHeader'
DialogBody.displayName = 'DialogBody'
DialogFooter.displayName = 'DialogFooter'
DialogCloseButton.displayName = 'DialogCloseButton'

export {
  Dialog,
  DialogBackdrop,
  DialogBody,
  DialogCloseButton,
  DialogContent,
  DialogFooter,
  DialogHeader,
}
