import { Text } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import DateTimePicker from '@react-native-community/datetimepicker'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Platform, TouchableOpacity, View } from 'react-native'
import CalendarIcon from '../../../assets/profile/calendar-icon.svg'
import { TextInput } from '../../../components/ui/TextInput/TextInput'
import { SheetRef, useSheetActions } from '../../../contexts/SheetContext/SheetContext'

interface DatePickerFieldProps {
  label?: string
  value?: string
  onDateChange: (date: string) => void
  format?: string
  placeholder?: string
  locale?: string
  defaultValue?: string
  disabled?: boolean
}

export const DatePickerField = ({
  label,
  value,
  onDateChange,
  format = 'YYYY/MM/DD',
  placeholder,
  locale = LocaleEnum.VI,
  defaultValue = '',
  disabled = false,
}: DatePickerFieldProps) => {
  const { t } = useTranslation()
  const [isPickerVisible, setIsPickerVisible] = useState(false)
  const [inputValue, setInputValue] = useState(value || defaultValue || '')
  const [currentDate, setCurrentDate] = useState(() => {
    // Use value first, then defaultValue if value is empty, then current date as fallback
    if (value) {
      return dayjs(value, format).toDate()
    } else if (defaultValue) {
      return dayjs(defaultValue, format).toDate()
    }
    return new Date()
  })

  // Update inputValue when value prop changes
  useEffect(() => {
    if (value !== undefined && value !== inputValue) {
      setInputValue(value)
      if (value) {
        const parsedDate = dayjs(value, format)
        if (parsedDate.isValid()) {
          setCurrentDate(parsedDate.toDate())
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value, format])

  const { openCustomSheet } = useSheetActions()
  let sheetRef: SheetRef | null = null

  const handleDateChange = (selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setIsPickerVisible(false)
    } else if (Platform.OS === 'ios' && sheetRef) {
      // Don't close sheet on iOS when selecting date - let user close manually with Done button
      // iOS spinner mode continuously fires onChange events as user scrolls
    }

    if (selectedDate) {
      setCurrentDate(selectedDate)
      // Format date using dayjs
      const formattedDate = dayjs(selectedDate).format(format)
      setInputValue(formattedDate)
      onDateChange(formattedDate)
    }
  }

  const validateAndUpdateDate = (text: string) => {
    setInputValue(text)

    // If empty string, just pass it through
    if (!text.trim()) {
      onDateChange('')
      return
    }

    // Basic validation for YYYY/MM/DD format
    const dateRegex = /^\d{4}\/\d{2}\/\d{2}$/

    if (!dateRegex.test(text)) {
      // If format doesn't match, pass the raw text for form validation to catch
      onDateChange(text)
      return
    }

    // Validate the date using dayjs with strict parsing
    const parsedDate = dayjs(text, format, true)

    if (parsedDate.isValid()) {
      // Update the internal date state
      setCurrentDate(parsedDate.toDate())
      // Pass the formatted date string (consistent format)
      onDateChange(parsedDate.format(format))
    } else {
      // Invalid date - pass the raw text for form validation to catch
      onDateChange(text)
    }
  }

  const showDatePicker = () => {
    if (disabled) return
    if (Platform.OS === 'android') {
      setIsPickerVisible(true)
    } else {
      // Use bottom sheet for iOS
      sheetRef = openCustomSheet({
        children: (
          <View className="w-full pb-5">
            {/* <View className="w-full flex-row justify-end  border-b border-custom-divider px-3 pb-3  ">
               <TouchableOpacity onPress={() => sheetRef?.close()}>
                 <Text size="body6" variant="primary" className="">
                   Done
                 </Text>
                </TouchableOpacity>
             </View> */}
            <View className="w-full items-center justify-center">
              <DateTimePicker
                value={currentDate}
                mode="date"
                display="spinner"
                onChange={(_, selectedDate) => handleDateChange(selectedDate)}
                maximumDate={new Date()}
                style={{ width: '100%', height: 200 }}
                locale={locale}
                disabled={disabled}
              />
            </View>
          </View>
        ),
        baseProps: {
          snapPoints: [240],
          enablePanDownToClose: true,
          enableDynamicSizing: false,
        },
      })
    }
  }

  // Render Android picker directly
  const renderAndroidPicker = () => {
    if (isPickerVisible) {
      return (
        <DateTimePicker
          value={currentDate}
          mode="date"
          display="default"
          onChange={(_, selectedDate) => handleDateChange(selectedDate)}
          maximumDate={new Date()}
          locale={locale}
          disabled={disabled}
        />
      )
    }
    return null
  }

  return (
    <View className="gap-2">
      {label && <Text className="text-text-default text-sm">{label}</Text>}

      <View className="relative">
        <TextInput
          value={inputValue}
          editable={!disabled}
          onChangeText={validateAndUpdateDate}
          placeholder={placeholder || t('MES-651') + 'YYYY/MM/DD'}
          wrapperClassName="pr-8"
        />
        <TouchableOpacity
          className="absolute right-3 top-1/2 -translate-y-1/2"
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          onPress={disabled ? undefined : showDatePicker}
        >
          <CalendarIcon width={16} height={16} />
        </TouchableOpacity>
      </View>

      {Platform.OS === 'android' && renderAndroidPicker()}
    </View>
  )
}
