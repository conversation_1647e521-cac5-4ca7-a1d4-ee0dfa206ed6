import * as TabsPrimitive from '@rn-primitives/tabs'
import * as React from 'react'

import { withReanimatedStrictOff } from '@/libs/withReanimatedStrictOff'
import { cn } from '@/utils/cn'

const Tabs = TabsPrimitive.Root

function TabsList({
  className,
  ...props
}: TabsPrimitive.ListProps & {
  ref?: React.RefObject<TabsPrimitive.ListRef>
}) {
  return (
    <TabsPrimitive.List
      className={cn(
        'native:h-12 bg-muted native:px-1.5 inline-flex h-10 items-center justify-center rounded-md p-1',
        className,
      )}
      {...props}
    />
  )
}

function TabsTrigger({
  className,
  // isActive,
  ...props
}: TabsPrimitive.TriggerProps & {
  ref?: React.RefObject<TabsPrimitive.TriggerRef>
  isActive?: boolean
}) {
  return withReanimatedStrictOff(() => (
    <TabsPrimitive.Trigger
      className={cn(
        // 'inline-flex items-center justify-center whitespace-nowrap rounded-[60px] border border-transparent bg-custom-neutral-80 px-2 py-1 text-sm font-medium text-custom-text-subdued shadow-none transition-all',
        // props.disabled && 'pointer-events-none opacity-50',
        // isActive && 'border !border-primary !bg-white text-primary',
        className,
      )}
      {...props}
    />
  ))
}

function TabsContent({
  className,
  ...props
}: TabsPrimitive.ContentProps & {
  ref?: React.RefObject<TabsPrimitive.ContentRef>
}) {
  return (
    <TabsPrimitive.Content
      className={cn(
        'ring-offset-background focus-visible:ring-ring focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
        className,
      )}
      {...props}
    />
  )
}

export { Tabs, TabsContent, TabsList, TabsTrigger }

// function useSharedValueState<T>(sharedValue: SharedValue<T>): T {
//   const [state, setState] = React.useState<T | undefined>(undefined)

//   React.useEffect(() => {
//     setState(sharedValue.value)
//   }, [sharedValue])

//   useDerivedValue(() => {
//     runOnJS(setState)(sharedValue.value)
//   }, [sharedValue])

//   return state as T
// }
