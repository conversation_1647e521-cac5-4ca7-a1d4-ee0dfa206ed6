import ArrowLeftIcon from '@/assets/icons/arrow-left-primary.svg'
import { router, Stack, useRouter } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Platform, TouchableOpacity, View } from 'react-native'
type StackScreenProps = React.ComponentProps<typeof Stack.Screen>

export const StackScreenBase = (props: StackScreenProps) => {
  const { t } = useTranslation()
  const { options, ...rest } = props
  const router = useRouter()
  return (
    <Stack.Screen
      options={{
        // headerBackTitle: t('MES-77'),
        headerBackButtonDisplayMode: 'minimal',
        headerShown: true,
        headerLeft: Platform.OS === 'ios' ? () => <BackButton /> : undefined,
        ...options,
      }}
      {...rest}
    />
  )
}
// IOS Only
const BackButton = () => {
  return (
    <View>
      <TouchableOpacity
        onPress={() => router.back()}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <View className="bg-rp aspect-square p-3  pl-0">
          <ArrowLeftIcon width={20} height={20} />
        </View>
      </TouchableOpacity>
    </View>
  )
}
