import { useFCM } from '@/providers/FCMProvider/FCMProvider'
import { useNotificationStore } from '@/stores/NotificationStore/NotificationStore'
import React, { useState } from 'react'
import { Alert, Button, StyleSheet, Text, View } from 'react-native'

interface FCMTesterProps {
  visible?: boolean
}

export const FCMTester: React.FC<FCMTesterProps> = ({ visible = false }) => {
  const [isLoading, setIsLoading] = useState(false)
  const { subscribeToTopic, unsubscribeFromTopic, getToken, checkPermission, requestPermission } =
    useFCM()

  const {
    fcmToken,
    fcmIsRegistered,
    fcmError,
    expoPushToken,
    isRegistered: expoIsRegistered,
    error: expoError,
  } = useNotificationStore()

  // Debug logging
  console.log('FCMTester: Store values:', {
    fcmToken: fcmToken ? `${fcmToken.substring(0, 20)}...` : 'empty',
    fcmIsRegistered,
    fcmError,
    expoPushToken: expoPushToken ? `${expoPushToken.substring(0, 20)}...` : 'empty',
    expoIsRegistered,
    expoError,
  })

  if (!visible) {
    return null
  }

  const handleGetFCMToken = async () => {
    setIsLoading(true)
    try {
      const token = await getToken()
      if (token) {
        Alert.alert('FCM Token', token)
      } else {
        Alert.alert('Error', 'Failed to get FCM token')
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to get FCM token')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCheckPermission = async () => {
    try {
      const status = await checkPermission()
      Alert.alert('FCM Permission Status', `Status: ${status}`)
    } catch (error) {
      Alert.alert('Error', 'Failed to check permission')
    }
  }

  const handleRequestPermission = async () => {
    try {
      const status = await requestPermission()
      Alert.alert('FCM Permission Requested', `Status: ${status}`)
    } catch (error) {
      Alert.alert('Error', 'Failed to request permission')
    }
  }

  const handleSubscribeToTopic = async () => {
    try {
      await subscribeToTopic('general')
      Alert.alert('Success', 'Subscribed to "general" topic')
    } catch (error) {
      Alert.alert('Error', 'Failed to subscribe to topic')
    }
  }

  const handleUnsubscribeFromTopic = async () => {
    try {
      await unsubscribeFromTopic('general')
      Alert.alert('Success', 'Unsubscribed from "general" topic')
    } catch (error) {
      Alert.alert('Error', 'Failed to unsubscribe from topic')
    }
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>FCM & Notification Tester</Text>

      {/* FCM Status */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>FCM Status</Text>
        <Text style={styles.status}>Registered: {fcmIsRegistered ? '✅' : '❌'}</Text>
        <Text style={styles.status}>Token: {fcmToken ? '✅ Available' : '❌ Not available'}</Text>
        {fcmError && <Text style={styles.error}>Error: {fcmError}</Text>}
      </View>

      {/* Expo Status */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Expo Push Status</Text>
        <Text style={styles.status}>Registered: {expoIsRegistered ? '✅' : '❌'}</Text>
        <Text style={styles.status}>
          Token: {expoPushToken ? '✅ Available' : '❌ Not available'}
        </Text>
        {expoError && <Text style={styles.error}>Error: {expoError}</Text>}
      </View>

      {/* FCM Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>FCM Actions</Text>

        <Button title="Get FCM Token" onPress={handleGetFCMToken} disabled={isLoading} />

        <View style={styles.buttonSpacing} />

        <Button title="Check Permission" onPress={handleCheckPermission} />

        <View style={styles.buttonSpacing} />

        <Button title="Request Permission" onPress={handleRequestPermission} />

        <View style={styles.buttonSpacing} />

        <Button title="Subscribe to 'general' topic" onPress={handleSubscribeToTopic} />

        <View style={styles.buttonSpacing} />

        <Button title="Unsubscribe from 'general' topic" onPress={handleUnsubscribeFromTopic} />
      </View>

      {/* Token Display */}
      {fcmToken && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>FCM Token</Text>
          <Text style={styles.tokenText} numberOfLines={3}>
            {fcmToken}
          </Text>
        </View>
      )}

      {expoPushToken && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Expo Push Token</Text>
          <Text style={styles.tokenText} numberOfLines={3}>
            {expoPushToken}
          </Text>
        </View>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
    margin: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  section: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#fff',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  status: {
    fontSize: 14,
    marginBottom: 5,
    color: '#666',
  },
  error: {
    fontSize: 14,
    color: '#ff4444',
    marginTop: 5,
  },
  buttonSpacing: {
    height: 10,
  },
  tokenText: {
    fontSize: 12,
    color: '#666',
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 5,
    fontFamily: 'monospace',
  },
})
