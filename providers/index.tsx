import { AuthenticationProvider } from '@/contexts/AuthenticationContext/AuthenticationContext'
import { DialogProvider } from '@/contexts/DialogContext/DialogContext'
import { LanguageProvider } from '@/contexts/LanguageContext/LanguageContext'
import { LoadingScreenProvider } from '@/contexts/LoadingScreenContext/LoadingScreenContext'
import { SheetProvider } from '@/contexts/SheetContext/SheetContext'
import { toastConfig } from '@/libs/toastConfig'
import React, { ReactNode } from 'react'
import { StyleSheet } from 'react-native'
import { SafeAreaProvider } from 'react-native-safe-area-context'
import Toast from 'react-native-toast-message'
import { GluestackUIProvider } from './GluestackProvider'
import { NotificationProvider } from './NotificationProvider/NotificationProvider'
import { TanstackProvider } from './TanstackProvider/TanstackProvider'
const AppWrapper: React.FC<{ children?: React.ReactNode }> = ({ children }) => {
  return (
    <>
      <SafeAreaProvider style={styles.safeArea}>
        <GluestackUIProvider>{children}</GluestackUIProvider>
        <Toast config={toastConfig} />
      </SafeAreaProvider>
    </>
  )
}

type ProviderProps = {
  children?: React.ReactNode
}
type ProviderType = React.FC<ProviderProps>

const composeProviders = (providers: ProviderType[]) => {
  return ({ children }: ProviderProps): ReactNode => {
    return providers.reduceRight((child, Provider) => <Provider>{child}</Provider>, children)
  }
}

// Define providers (excluding GluestackUIProvider since it's now in AppWrapper)
const providers: ProviderType[] = [
  LoadingScreenProvider,
  LanguageProvider as React.FC<{ children?: React.ReactNode }>,
  AuthenticationProvider as React.FC<{ children?: React.ReactNode }>,
  TanstackProvider as React.FC<{ children?: React.ReactNode }>,
  SheetProvider as React.FC<{ children?: React.ReactNode }>,
  DialogProvider as React.FC<{ children?: React.ReactNode }>,
  NotificationProvider as React.FC<{ children?: React.ReactNode }>,
]
const ComposedProviders = composeProviders(providers)
// Main Providers component
export const Providers: React.FC<ProviderProps> = ({ children }) => {
  return (
    <AppWrapper>
      <ComposedProviders>{children}</ComposedProviders>
    </AppWrapper>
  )
}
const styles = StyleSheet.create({
  safeArea: {
    flex: 1, // Ensures the SafeAreaView takes up the full screen
    // backgroundColor: 'white', // Set your desired background color here
  },
  content: {
    flex: 1,
    // Add other styles for your content View if needed
  },
})
