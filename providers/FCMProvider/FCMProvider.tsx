import {
  FCMMessage,
  FCMNotificationData,
  fcmService,
} from '@/services/push-notification/fcm.service'
import React, { useEffect, useRef } from 'react'
import { Platform } from 'react-native'

interface FCMProviderProps {
  children: React.ReactNode
  onTokenReceived?: (token: string) => void
  onMessageReceived?: (message: FCMMessage) => void
  onNotificationOpened?: (message: FCMMessage) => void
}

export const FCMProvider: React.FC<FCMProviderProps> = ({
  children,
  onTokenReceived,
  onMessageReceived,
  onNotificationOpened,
}) => {
  const tokenRefreshUnsubscribe = useRef<(() => void) | null>(null)

  console.log('FCMProvider: Component mounted with callbacks:', {
    onTokenReceived: !!onTokenReceived,
    onMessageReceived: !!onMessageReceived,
    onNotificationOpened: !!onNotificationOpened,
  })

  useEffect(() => {
    console.log('FCMProvider: useEffect triggered - starting FCM initialization')
    // Initialize FCM when provider mounts
    initializeFCM()

    // Cleanup on unmount
    return () => {
      console.log('FCMProvider: useEffect cleanup triggered')
      cleanup()
    }
  }, [])

  const initializeFCM = async () => {
    console.log('FCMProvider: initializeFCM function called')
    console.log('FCMProvider: fcmService.isAvailable =', fcmService.isAvailable)

    try {
      // Check if Firebase is available first
      if (!fcmService.isAvailable) {
        console.warn('FCM Provider: Firebase messaging not available, skipping FCM initialization')
        return
      }

      console.log('FCM Provider: Starting FCM initialization...')

      // Request permissions (iOS)
      if (Platform.OS === 'ios') {
        console.log('FCM Provider: Requesting iOS permissions...')
        const authStatus = await fcmService.requestPermission()
        console.log('FCM Permission status:', authStatus)
      }

      // Get FCM token
      console.log('FCM Provider: Getting FCM token...')
      const token = await fcmService.getToken()
      console.log('FCM Provider: Token received successfully:', token ? 'Yes' : 'No')
      console.log('FCM Provider: onTokenReceived callback exists:', !!onTokenReceived)

      if (onTokenReceived) {
        console.log('FCM Provider: Calling onTokenReceived with token:', token ? 'Yes' : 'No')
        onTokenReceived(token)
      } else {
        console.warn('FCM Provider: No onTokenReceived callback provided!')
      }

      // Setup token refresh listener
      console.log('FCM Provider: Setting up token refresh listener...')
      tokenRefreshUnsubscribe.current = fcmService.onTokenRefresh((refreshedToken) => {
        console.log('FCM Token refreshed:', refreshedToken)
        if (onTokenReceived) {
          onTokenReceived(refreshedToken)
        }
      })

      // Setup message handlers
      console.log('FCM Provider: Setting up message handlers...')
      setupMessageHandlers()

      console.log('FCM Provider initialized successfully')
    } catch (error) {
      console.error('Failed to initialize FCM Provider:', error)
      // Call onTokenReceived with null to indicate failure
      if (onTokenReceived) {
        console.log('FCM Provider: Calling onTokenReceived with null due to error')
      }
    }
  }

  const setupMessageHandlers = () => {
    // Foreground message handler
    fcmService.setForegroundMessageHandler((remoteMessage) => {
      console.log('FCM Foreground message received:', remoteMessage)
      const parsedMessage = fcmService.parseMessageData(remoteMessage)

      if (onMessageReceived) {
        onMessageReceived(parsedMessage)
      }

      // Handle different notification types in foreground
      handleForegroundMessage(parsedMessage)
    })

    // Background message handler
    fcmService.setBackgroundMessageHandler((remoteMessage) => {
      console.log('FCM Background message received:', remoteMessage)
      const parsedMessage = fcmService.parseMessageData(remoteMessage)

      if (onMessageReceived) {
        onMessageReceived(parsedMessage)
      }

      // Handle background message processing
      handleBackgroundMessage(parsedMessage)
    })

    // Notification opened handler
    fcmService.setNotificationOpenedHandler((remoteMessage) => {
      console.log('FCM Notification opened:', remoteMessage)
      const parsedMessage = fcmService.parseMessageData(remoteMessage)

      if (onNotificationOpened) {
        onNotificationOpened(parsedMessage)
      }

      // Handle notification tap navigation
      handleNotificationOpened(parsedMessage)
    })
  }

  const handleForegroundMessage = (message: FCMMessage) => {
    const data = message.data as FCMNotificationData

    switch (data?.type) {
      case 'new_message':
        console.log('New message in foreground:', data)
        // You can show an in-app notification or update UI
        break
      case 'appointment_reminder':
        console.log('Appointment reminder in foreground:', data)
        // Show appointment reminder UI
        break
      case 'medicine_reminder':
        console.log('Medicine reminder in foreground:', data)
        // Show medicine reminder UI
        break
      default:
        console.log('Default FCM foreground message:', data)
    }
  }

  const handleBackgroundMessage = (message: FCMMessage) => {
    const data = message.data as FCMNotificationData

    switch (data?.type) {
      case 'new_message':
        console.log('New message in background:', data)
        // Update badge count, sync data, etc.
        break
      case 'appointment_reminder':
        console.log('Appointment reminder in background:', data)
        // Prepare appointment data
        break
      case 'medicine_reminder':
        console.log('Medicine reminder in background:', data)
        // Update medicine reminder state
        break
      default:
        console.log('Default FCM background message:', data)
    }
  }

  const handleNotificationOpened = (message: FCMMessage) => {
    const data = message.data as FCMNotificationData

    switch (data?.type) {
      case 'new_message':
        console.log('Navigate to chat from FCM:', data.chatId)
        // Navigate to chat screen
        // router.push(`/chat/${data.chatId}`)
        break
      case 'appointment_reminder':
        console.log('Navigate to appointment from FCM:', data.appointmentId)
        // Navigate to appointment details
        // router.push(`/appointment/${data.appointmentId}`)
        break
      case 'medicine_reminder':
        console.log('Navigate to medicine from FCM:', data.medicineId)
        // Navigate to medicine details
        // router.push(`/medicine/${data.medicineId}`)
        break
      default:
        console.log('Default FCM notification tap handling:', data)
    }
  }

  const cleanup = () => {
    // Cleanup FCM listeners
    fcmService.cleanup()

    // Cleanup token refresh listener
    if (tokenRefreshUnsubscribe.current) {
      tokenRefreshUnsubscribe.current()
      tokenRefreshUnsubscribe.current = null
    }
  }

  return <>{children}</>
}

// Helper hook for using FCM in components
export const useFCM = () => {
  const subscribeToTopic = async (topic: string) => {
    try {
      await fcmService.subscribeToTopic(topic)
    } catch (error) {
      console.error('Failed to subscribe to topic:', error)
    }
  }

  const unsubscribeFromTopic = async (topic: string) => {
    try {
      await fcmService.unsubscribeFromTopic(topic)
    } catch (error) {
      console.error('Failed to unsubscribe from topic:', error)
    }
  }

  const getToken = async (): Promise<string | null> => {
    try {
      return await fcmService.getToken()
    } catch (error) {
      console.error('Failed to get FCM token:', error)
      return null
    }
  }

  const checkPermission = async () => {
    try {
      return await fcmService.checkPermission()
    } catch (error) {
      console.error('Failed to check FCM permission:', error)
      return null
    }
  }

  const requestPermission = async () => {
    try {
      return await fcmService.requestPermission()
    } catch (error) {
      console.error('Failed to request FCM permission:', error)
      return null
    }
  }

  return {
    subscribeToTopic,
    unsubscribeFromTopic,
    getToken,
    checkPermission,
    requestPermission,
  }
}
