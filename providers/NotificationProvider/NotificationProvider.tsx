import { FCMMessage } from '@/services/push-notification/fcm.service'
import { useNotificationStore } from '@/stores/NotificationStore/NotificationStore'
import Constants, { ExecutionEnvironment } from 'expo-constants'
import React, { useEffect, useState } from 'react'

interface NotificationProviderProps {
  children: React.ReactNode
}
const isRunningInExpoGo = Constants.executionEnvironment === ExecutionEnvironment.StoreClient

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [FCMProvider, setFCMProvider] = useState<React.ComponentType<any> | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const {
    // Expo notifications
    registerForPushNotifications,
    setupNotificationListeners,
    cleanupNotificationListeners,
    isRegistered,
    isLoading: isLoadingExpo,
    error,

    // FCM notifications
    setFcmToken,
    setFcmIsRegistered,
    setFcmError,
    fcmToken,
    fcmIsRegistered,
    fcmError,
  } = useNotificationStore()

  useEffect(() => {
    // Setup Expo notification listeners when provider mounts with custom handlers
    setupNotificationListeners(
      // Handler for when Expo notification is received (arrives)
      (notification) => {
        const notificationData = notification.request.content.data

        // Handle different notification types when they arrive
        switch (notificationData?.type) {
          case 'new_message':
            // Show message preview, update badge, etc.
            // console.log('New message received via Expo:', notificationData)
            break
          case 'appointment_reminder':
            // Show appointment reminder UI
            // console.log('Appointment reminder received via Expo:', notificationData)
            break
          case 'medicine_reminder':
            // Show medicine reminder UI
            // console.log('Medicine reminder received via Expo:', notificationData)
            break
          default:
          // console.log('Default Expo notification received:', notificationData)
        }
      },
      // Handler for when user taps on Expo notification
      (response) => {
        const notificationData = response.notification.request.content.data

        // Handle navigation and actions when user taps notification
        switch (notificationData?.type) {
          case 'new_message':
            // Navigate to chat screen
            // console.log('Navigate to chat from Expo:', notificationData.chatId)
            // router.push(`/chat/${notificationData.chatId}`)
            break
          case 'appointment_reminder':
            // Navigate to appointment details
            // console.log('Navigate to appointment from Expo:', notificationData.appointmentId)
            // router.push(`/appointment/${notificationData.appointmentId}`)
            break
          case 'medicine_reminder':
            // Navigate to medicine details or mark as taken
            // console.log('Handle medicine reminder tap from Expo:', notificationData.medicineId)
            // router.push(`/medicine/${notificationData.medicineId}`)
            break
          default:
          // console.log('Default Expo notification tap handling:', notificationData)
        }
      },
    )

    // Cleanup on unmount
    return () => {
      cleanupNotificationListeners()
    }
  }, [])

  // Separate effect for Expo registration to prevent infinite loop
  useEffect(() => {
    // Only register once when component mounts and not already registered
    if (!isRegistered && !isLoadingExpo && !error) {
      registerForPushNotifications()
    }
  }, []) // Empty dependency array - only run once

  // Log Expo errors for debugging
  useEffect(() => {
    if (error) {
      // console.error('Expo notification registration error:', error)
    }
  }, [error])

  // Log FCM errors for debugging
  useEffect(() => {
    if (fcmError) {
      // console.error('FCM notification registration error:', fcmError)
    }
  }, [fcmError])

  // FCM token received handler
  const handleFCMTokenReceived = (token: string) => {
    // console.log(
    //   'NotificationProvider: handleFCMTokenReceived called with token:',
    //   token ? 'Yes' : 'No',
    // )
    // console.log('NotificationProvider: Full token:', token)

    if (token) {
      setFcmToken(token)
      setFcmIsRegistered(true)
      setFcmError(null)
      // console.log('NotificationProvider: FCM successfully registered and token saved to store')
    } else {
      setFcmToken('')
      setFcmIsRegistered(false)
      setFcmError('Failed to get FCM token')
      // console.log('NotificationProvider: FCM registration failed - no token received')
    }
  }

  // FCM message received handler
  const handleFCMMessageReceived = (message: FCMMessage) => {
    // console.log('FCM Message received:', message)

    // Handle different FCM message types
    const data = message.data
    switch (data?.type) {
      case 'new_message':
        // console.log('New message received via FCM:', data)
        break
      case 'appointment_reminder':
        // console.log('Appointment reminder received via FCM:', data)
        break
      case 'medicine_reminder':
        // console.log('Medicine reminder received via FCM:', data)
        break
      default:
      // console.log('Default FCM message received:', data)
    }
  }

  // FCM notification opened handler
  const handleFCMNotificationOpened = (message: FCMMessage) => {
    // console.log('FCM Notification opened:', message)

    // Handle navigation when FCM notification is tapped
    const data = message.data
    switch (data?.type) {
      case 'new_message':
        // console.log('Navigate to chat from FCM:', data.chatId)
        // router.push(`/chat/${data.chatId}`)
        break
      case 'appointment_reminder':
        // console.log('Navigate to appointment from FCM:', data.appointmentId)
        // router.push(`/appointment/${data.appointmentId}`)
        break
      case 'medicine_reminder':
        // console.log('Navigate to medicine from FCM:', data.medicineId)
        // router.push(`/medicine/${data.medicineId}`)
        break
      default:
      // console.log('Default FCM notification tap handling:', data)
    }
  }

  // Load FCMProvider dynamically only in production builds
  useEffect(() => {
    const loadFCMProvider = async () => {
      if (isRunningInExpoGo) {
        // Skip loading in Expo Go to prevent crashes
        setIsLoading(false)
        return
      }

      try {
        // Dynamic import without React.lazy to avoid Suspense issues
        const module = await import('../FCMProvider/FCMProvider')
        setFCMProvider(() => module.FCMProvider)
      } catch (error) {
        //  console.error('Failed to load FCMProvider:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadFCMProvider()
  }, [])

  // Show loading state while FCMProvider is being loaded
  if (isLoading) {
    return <>{children}</>
  }

  // If running in Expo Go or FCMProvider failed to load, just return children
  if (isRunningInExpoGo || !FCMProvider) {
    return <>{children}</>
  }

  // Render with FCMProvider in production builds
  return (
    <FCMProvider
      onTokenReceived={handleFCMTokenReceived}
      onMessageReceived={handleFCMMessageReceived}
      onNotificationOpened={handleFCMNotificationOpened}
    >
      {children}
    </FCMProvider>
  )
}
