import { BaseWebView } from '@/components/BaseWebView/BaseWebView'
import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { WEBVIEW_APP_ROUTES } from '@/routes/webviewRoutes'
import { useLocalSearchParams } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
export default function MedicalScreen() {
  const { t } = useTranslation()
  const { id } = useLocalSearchParams()
  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: t('MES-564'),
        }}
      />
      <BaseWebView
        source={{
          uri: WEBVIEW_APP_ROUTES.MEDICAL_HANDBOOK?.children?.FACULTIES.path + `/${id}`,
        }}
        setupFurigana
      />
    </>
  )
}
