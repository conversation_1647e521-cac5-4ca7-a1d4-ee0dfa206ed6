import HomeIcon from '@/assets/icons/home-primary-color-icon.svg'
import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { LoginScreen } from '@/features/auth/screens/LoginScreen/LoginScreen'
import { APP_ROUTES } from '@/routes/appRoutes'

import { Link, LinkProps } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity } from 'react-native'

export default function StackLoginScreen() {
  const { t } = useTranslation()

  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: t('MES-06'),
          headerBackTitle: '',
          headerBackButtonDisplayMode: 'minimal',
          headerRight: () => (
            <Link href={APP_ROUTES.HOME.path as LinkProps['href']} asChild>
              <TouchableOpacity>
                <HomeIcon />
              </TouchableOpacity>
            </Link>
          ),
        }}
      />
      <LoginScreen />
    </>
  )
}
