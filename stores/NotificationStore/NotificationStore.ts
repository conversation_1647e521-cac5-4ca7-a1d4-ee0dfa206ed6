import * as Notifications from 'expo-notifications'
import { create } from 'zustand'

export interface NotificationState {
  // Expo notifications
  expoPushToken: string
  isRegistered: boolean
  isLoading: boolean
  error: string | null

  // FCM notifications
  fcmToken: string
  fcmIsRegistered: boolean
  fcmIsLoading: boolean
  fcmError: string | null
}

export interface NotificationActions {
  // Expo State setters
  setExpoPushToken: (token: string) => void
  setIsRegistered: (isRegistered: boolean) => void
  setIsLoading: (isLoading: boolean) => void
  setError: (error: string | null) => void

  // FCM State setters
  setFcmToken: (token: string) => void
  setFcmIsRegistered: (isRegistered: boolean) => void
  setFcmIsLoading: (isLoading: boolean) => void
  setFcmError: (error: string | null) => void

  // Actions
  registerForPushNotifications: () => Promise<void>
  setupNotificationListeners: (
    onNotificationReceived?: (notification: Notifications.Notification) => void,
    onNotificationResponse?: (response: Notifications.NotificationResponse) => void,
  ) => void
  cleanupNotificationListeners: () => void
  reset: () => void
}

export interface NotificationStore extends NotificationState, NotificationActions {}

const initialState: NotificationState = {
  // Expo notifications
  expoPushToken: '',
  isRegistered: false,
  isLoading: false,
  error: null,

  // FCM notifications
  fcmToken: '',
  fcmIsRegistered: false,
  fcmIsLoading: false,
  fcmError: null,
}

export const useNotificationStore = create<NotificationStore>((set, get) => ({
  ...initialState,

  // Expo State setters
  setExpoPushToken: (token: string) => set({ expoPushToken: token }),
  setIsRegistered: (isRegistered: boolean) => set({ isRegistered }),
  setIsLoading: (isLoading: boolean) => set({ isLoading }),
  setError: (error: string | null) => set({ error }),

  // FCM State setters
  setFcmToken: (token: string) => set({ fcmToken: token }),
  setFcmIsRegistered: (isRegistered: boolean) => set({ fcmIsRegistered: isRegistered }),
  setFcmIsLoading: (isLoading: boolean) => set({ fcmIsLoading: isLoading }),
  setFcmError: (error: string | null) => set({ fcmError: error }),

  // Register for push notifications (Expo)
  registerForPushNotifications: async () => {
    const { setIsLoading, setError, setExpoPushToken, setIsRegistered } = get()

    try {
      setIsLoading(true)
      setError(null)

      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync()
      let finalStatus = existingStatus

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync()
        finalStatus = status
      }

      if (finalStatus !== 'granted') {
        throw new Error('Permission not granted for push notifications')
      }

      // Get push token
      const projectId = process.env.EXPO_PUBLIC_PROJECT_ID
      if (!projectId) {
        // Use the project ID from app.config.ts instead of environment variable
        const projectIdFromConfig = 'd1a2d577-c2ff-4b68-ba1b-f80df20e00e5'
        console.warn('EXPO_PUBLIC_PROJECT_ID not found, using project ID from config')

        const tokenData = await Notifications.getExpoPushTokenAsync({
          projectId: projectIdFromConfig,
        })

        setExpoPushToken(tokenData.data)
        setIsRegistered(true)
        // console.log('Expo push notification token:', tokenData.data)
        return
      }

      const tokenData = await Notifications.getExpoPushTokenAsync({
        projectId,
      })

      setExpoPushToken(tokenData.data)
      setIsRegistered(true)
      // console.log('Expo push notification token:', tokenData.data)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      setError(errorMessage)
      console.error('Failed to register for Expo push notifications:', error)
      // Don't retry on error to prevent infinite loop
      setIsRegistered(false)
    } finally {
      setIsLoading(false)
    }
  },

  // Setup notification listeners (Expo)
  setupNotificationListeners: (onNotificationReceived, onNotificationResponse) => {
    // Listen for received notifications (when notification arrives)
    const notificationListener = Notifications.addNotificationReceivedListener((notification) => {
      console.log('Expo notification received:', notification)

      // Call the custom handler if provided
      if (onNotificationReceived) {
        onNotificationReceived(notification)
      }
    })

    // Listen for notification responses (when user taps notification)
    const responseListener = Notifications.addNotificationResponseReceivedListener((response) => {
      console.log('Expo notification response received:', response)

      // Call the custom response handler if provided
      if (onNotificationResponse) {
        onNotificationResponse(response)
      }
    })

    // Store listeners for cleanup
  },

  // Cleanup notification listeners
  cleanupNotificationListeners: () => {
    // The listeners are automatically cleaned up when the app is closed
    // No need to reset notification state since we're not storing it anymore
  },

  // Reset store to initial state
  reset: () => set(initialState),
}))
