import type { ExpoConfig } from '@expo/config'
import 'dotenv/config'

const baseConfig: Partial<ExpoConfig> = {
  name: 'HICO',
  slug: 'hico',
  version: '2.1.0',
  orientation: 'portrait',
  icon: './assets/images/logo.png',
  scheme: 'com.wap.hico',
  userInterfaceStyle: 'automatic',
  newArchEnabled: true,
  splash: {
    image: './assets/images/logo.png',
    resizeMode: 'contain',
    backgroundColor: '#ffffff',
    imageWidth: 140,
  },

  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.wap.hico',
    googleServicesFile: './GoogleService-Info.plist',
    infoPlist: {
      NSCameraUsageDescription: 'Allow this app to access your camera',
      NSLocationWhenInUseUsageDescription:
        'Allow this app to access your location while using the app',
      NSContactsUsageDescription: 'Allow this app to access your contacts',
      ITSAppUsesNonExemptEncryption: false,
      UIBackgroundModes: ['audio', 'remote-notification'],
    },
    entitlements: {
      'aps-environment': 'production',
    },
  },

  android: {
    package: 'com.wap.hico',
    googleServicesFile: './google-services.json',
    edgeToEdgeEnabled: true,
    permissions: [
      'android.permission.READ_EXTERNAL_STORAGE',
      'android.permission.WRITE_EXTERNAL_STORAGE',
      'android.permission.RECORD_AUDIO',
      'android.permission.MODIFY_AUDIO_SETTINGS',
    ],
    adaptiveIcon: {
      foregroundImage: './assets/images/adaptive-icon.png',
      backgroundColor: '#ffffff',
    },
    intentFilters: [
      {
        action: 'VIEW',
        autoVerify: true,
        data: [{ scheme: 'com.wap.hico', host: 'oauthredirect' }],
        category: ['BROWSABLE', 'DEFAULT'],
      },
    ],
  },

  web: {
    bundler: 'metro',
    output: 'static',
    favicon: './assets/images/favicon.png',
  },

  experiments: {
    typedRoutes: true,
  },

  extra: {
    router: {},
    eas: {
      projectId: 'd1a2d577-c2ff-4b68-ba1b-f80df20e00e5',
    },
  },
}

const sharedPlugins: ExpoConfig['plugins'] = [
  'expo-router',

  [
    'expo-splash-screen',
    {
      image: './assets/images/logo.png',
      imageWidth: 140,
      resizeMode: 'contain',
      backgroundColor: '#ffffff',
    },
  ],
  [
    'expo-audio',
    {
      microphonePermission: 'Allow to access your microphone.',
    },
  ],
  [
    'expo-secure-store',
    {
      configureAndroidBackup: true,
      faceIDPermission: 'Allow $(PRODUCT_NAME) to access your Face ID biometric data.',
    },
  ],
  [
    'expo-image-picker',
    {
      photosPermission: 'Allow this app to access your photos',
    },
  ],
  [
    'expo-web-browser',
    {
      experimentalLauncherActivity: true,
    },
  ],
  [
    'expo-notifications',
    {
      icon: './assets/images/logo.png',
      color: '#ffffff',
      defaultChannel: 'default',
      enableBackgroundRemoteNotifications: true,
      // sounds: ['./momo_noti_sound.mp3'],
    },
  ],
  '@react-native-firebase/app',
  '@react-native-firebase/messaging',
  [
    'expo-build-properties',
    {
      ios: {
        useFrameworks: 'static',
      },
    },
  ],
]

const devConfig: Partial<ExpoConfig> = {
  ...baseConfig,
  plugins: sharedPlugins,
}

const prodConfig: Partial<ExpoConfig> = {
  ...baseConfig,
  plugins: [
    ...sharedPlugins,
    [
      '@react-native-google-signin/google-signin',
      {
        iosUrlScheme: process.env.EXPO_IOS_URL_SCHEME,
      },
    ],
  ],
}

export default ({ mode }: { expoConfig: ExpoConfig; mode: string }) => {
  return process.env.EXPO_PUBLIC_APP_ENVIROMENT === 'production' ? prodConfig : devConfig
}
